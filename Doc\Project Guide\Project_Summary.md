# Comply Checker: Comprehensive Project Guide

This document is a detailed, standalone guide summarizing the key architectural patterns, technical decisions, and development standards for the Comply Checker project. It is intended to provide a complete project overview for any developer or AI, eliminating the need to read the seven source documentation files.

---

## 1. Project Overview

- **Core Product**: A production-ready (v2.1) SaaS platform for automated compliance scanning. It provides real-time analysis, detailed reporting, and actionable recommendations.
- **Supported Standards**: The platform is designed to support multiple standards. The **HIPAA** module is fully complete, and the immediate roadmap includes implementing **GDPR**, **WCAG**, and **ADA** modules.
- **High-Level Architecture**: The project is a **monorepo** managed with `npm` workspaces, containing a **Next.js 14** frontend and an **Express.js** backend.
- **Key Completed Features (HIPAA)**:
    - **3-Level Privacy Analysis**: A sophisticated system combining basic keyword matching, advanced NLP (`compromise.js`), and AI-powered analysis (`DistilBERT`).
    - **Security Scanning**: Deep integration with the **Nuclei** scanning engine to check for vulnerabilities, SSL/TLS configuration, security headers, and more.

---

## 2. Backend Architecture

- **Core Stack**: A **Node.js/Express.js** application written exclusively in **TypeScript** (`strict` mode enabled).
    - **Validation**: **Zod** is used for robust schema validation of environment variables and API request bodies.
    - **Logging**: **Pino** is used for structured, high-performance logging.
- **Database**: **PostgreSQL** is the database of choice, with **Knex.js** used for query building, schema migrations, and seeding. All database tables and columns must use `snake_case`.
- **Modular Design**: The architecture is highly modular to support new compliance standards easily. The central `ScanService` (`src/services/scan-service.ts`) acts as an orchestrator, invoking the appropriate compliance module based on the scan request.
- **Adding a New Compliance Module (e.g., GDPR)**:
    1.  **Create the Engine**: Add a new directory `src/compliance/gdpr/`.
    2.  **Follow the Blueprint**: Inside, create the required files:
        - `index.ts`: Exports the module's public interface.
        - `orchestrator.ts`: Contains the main coordination logic for all GDPR checks.
        - `types.ts`: Defines all GDPR-specific TypeScript types.
        - `checks/`: A subdirectory containing individual check files (e.g., `data-residency.ts`).
        - `constants.ts`: Holds module-specific constants.
    3.  **Define Routes**: Create `src/routes/compliance/gdpr.ts` for the new API endpoints.
    4.  **Integrate**: Register the new module and routes within the main `ScanService` and Express router.
- **Authentication**: User authentication and authorization are managed by **Keycloak**. All protected API endpoints are secured using JSON Web Tokens (JWTs).
- **Deployment**: The application is containerized using a multi-stage `Dockerfile` for optimized, minimal-footprint production builds.

---

## 3. Frontend Architecture

- **Framework**: A **Next.js 14** application using the **App Router** for file-system-based routing and Server Components.
- **UI & Styling**: The UI is built with **shadcn/ui**, a library of accessible components, and styled with **Tailwind CSS**. This combination ensures a consistent, modern, and accessible user experience.
- **State Management**:
    - **Client State**: Managed using **React Context** (e.g., for authentication state via `AuthContext`).
    - **Server State**: Handled by **TanStack Query (React Query)** for all data fetching, caching, and server state synchronization. This is the required tool for interacting with the backend API.
- **API Interaction**: A centralized API client (`lib/api-client.ts`), built with `axios`, is pre-configured to handle standardized API responses and error formats.
- **Adding New Features (e.g., GDPR Dashboard)**:
    1.  Create a new route directory: `app/dashboard/gdpr/`.
    2.  The entry point will be `app/dashboard/gdpr/page.tsx`.
    3.  Wrap the page in the `ProtectedRoute` component to ensure only authenticated users can access it.
    4.  Create reusable components for the feature inside `components/features/gdpr/`.
- **Authentication**: Client-side auth is managed via the `keycloak-js` library, integrated into a custom `useAuth` hook.
- **Accessibility**: The application is designed to meet **WCAG AA** compliance standards, a critical requirement for all new components and pages.

---

## 4. Development Workflow & Standards

- **Environment**: Local development requires **Node.js (18.x+)**, **npm (9.x+)**, and **Docker**. A single command, `docker-compose up -d`, starts all necessary services (PostgreSQL, Keycloak).
- **Branching Strategy**: The project uses the **GitFlow** model. All new work must be done in a feature branch.
    - **Command**: `git checkout -b feature/your-feature-name`
- **Commit Hygiene**: Commits must follow the **Conventional Commits** specification. This is enforced and is essential for automated changelog generation.
    - **Example**: `feat(gdpr): add data residency check for EU servers`
    - **Example**: `fix(api): correct error code for invalid scan ID`
- **Code Quality Gates**: **Husky pre-commit hooks** enforce strict quality checks. **No commit will be allowed to proceed if any of these checks fail.**
    - **Linting**: `ESLint` with a strict ruleset.
    - **Formatting**: `Prettier` for consistent code style.
    - **Type-checking**: `TypeScript` compilation must pass with zero errors. The use of `any` is strictly forbidden.
    - **Unit Tests**: `Jest` tests must all pass.
- **Pull Requests (PRs)**: All code is submitted via a PR against the `develop` branch. A formal code review from at least one other team member is required before the PR can be squashed and merged.

---

## 5. API & Data Standards

- **API Structure**: The RESTful API is versioned (`/api/v1`) and follows a predictable structure. New GDPR endpoints would live at `/api/v1/compliance/gdpr/`.
- **Standard Response Format**: All API responses use a consistent JSON wrapper to ensure predictable handling on the frontend.
    ```typescript
    interface ApiResponse<T> {
      success: boolean;
      data?: T;
      error?: { code: string; message: string; details?: any; };
      metadata: { timestamp: string; requestId: string; };
    }
    ```
- **Authentication**: Protected endpoints require a JWT `Bearer` token from Keycloak in the `Authorization` header.
- **Error Handling**: The API uses a standardized set of error codes. Key codes include:
    - `VALIDATION_ERROR` (400)
    - `UNAUTHORIZED` (401)
    - `FORBIDDEN` (403)
    - `NOT_FOUND` (404)
    - `INTERNAL_ERROR` (500)

---

## 6. Design System & Theming

- **Core Technology**: The theme system uses **CSS Custom Properties** and is managed via a React `ThemeProvider`.
- **Styling**: All styling **must** be done with **Tailwind CSS**. The `tailwind.config.js` is configured to use the theme's CSS variables (e.g., `hsl(var(--primary))`). Direct styling (e.g., `style={{ color: '#FFF' }}`) is forbidden.
- **Available Themes**: The app supports two **WCAG AA compliant** themes: `standard` (light) and `dark`.
- **Risk Color Palette**: The design system defines specific, semantic colors for scan result risk levels. These **must** be used to display findings:
    - **Success/Low Risk**: `bg-success` / `text-success-foreground`
    - **Warning/Medium Risk**: `bg-warning` / `text-warning-foreground`
    - **Error/Critical Risk**: `bg-error` / `text-error-foreground`
    - **High Risk**: `bg-high-risk` / `text-high-risk-foreground`
- **Usage**: Use the `useTheme()` hook to access the current theme state. Apply styles using Tailwind utilities, which automatically adapt to the active theme.