import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface CookieConsentCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class CookieConsentCheck {
  /**
   * Check cookie consent banner compliance
   * REAL WEBSITE INTERACTION - tests actual consent mechanisms
   */
  async performCheck(config: CookieConsentCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let passed = false;
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      // Clear cookies to ensure fresh consent state
      await page.deleteCookie(...(await page.cookies()));
      
      // Navigate to actual website
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Wait for potential consent banner to appear
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Analyze consent banner - REAL DOM analysis
      const bannerAnalysis = await this.analyzeConsentBanner(page);
      
      if (!bannerAnalysis.bannerFound) {
        evidence.push({
          type: 'element',
          description: 'No cookie consent banner detected',
          location: 'Page scan',
        });
        score = 0;
      } else {
        evidence.push({
          type: 'element',
          description: 'Cookie consent banner found',
          location: bannerAnalysis.location || 'Unknown',
          value: bannerAnalysis.bannerText,
        });

        // Evaluate banner compliance
        const complianceScore = this.evaluateBannerCompliance(bannerAnalysis);
        score = complianceScore.score;
        passed = complianceScore.score >= 70; // 70% threshold for passing

        // Add detailed evidence
        evidence.push(...complianceScore.evidence);

        // Test consent functionality - REAL interaction testing
        const functionalityTest = await this.testConsentFunctionality(page, bannerAnalysis);
        evidence.push(...functionalityTest.evidence);
        
        if (!functionalityTest.working) {
          score = Math.max(0, score - 30); // Penalty for non-functional consent
          passed = false;
        }
      }

      return {
        ruleId: 'GDPR-004',
        ruleName: 'Cookie Consent Banner',
        category: 'consent',
        passed,
        score,
        weight: 9,
        severity: 'critical',
        evidence,
        recommendations: this.generateRecommendations(bannerAnalysis, score),
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-004',
        ruleName: 'Cookie Consent Banner',
        category: 'consent',
        passed: false,
        score: 0,
        weight: 9,
        severity: 'critical',
        evidence: [{
          type: 'text',
          description: 'Check failed with error',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Implement cookie consent banner',
          description: 'Add GDPR-compliant cookie consent mechanism',
          implementation: 'Install cookie consent solution with accept/reject options',
          effort: 'moderate',
          impact: 'high',
        }],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze consent banner on the page - REAL DOM analysis
   */
  private async analyzeConsentBanner(page: Page): Promise<{
    bannerFound: boolean;
    bannerText?: string;
    hasAcceptButton: boolean;
    hasRejectButton: boolean;
    hasGranularOptions: boolean;
    hasPrivacyPolicyLink: boolean;
    location?: string;
    preTickedBoxes: boolean;
  }> {
    return await page.evaluate(() => {
      // Common selectors for cookie banners
      const bannerSelectors = [
        '[class*="cookie"]',
        '[class*="consent"]',
        '[class*="gdpr"]',
        '[id*="cookie"]',
        '[id*="consent"]',
        '[data-testid*="cookie"]',
        '.cookie-banner',
        '.consent-banner',
        '#cookie-notice',
        '#consent-notice',
      ];

      let bannerElement: Element | null = null;
      
      // Find banner element
      for (const selector of bannerSelectors) {
        const elements = document.querySelectorAll(selector);
        for (const element of elements) {
          // Check if element is visible and contains consent-related text
          const rect = element.getBoundingClientRect();
          const text = element.textContent?.toLowerCase() || '';
          
          if (rect.width > 0 && rect.height > 0 && 
              (text.includes('cookie') || text.includes('consent') || text.includes('privacy'))) {
            bannerElement = element;
            break;
          }
        }
        if (bannerElement) break;
      }

      if (!bannerElement) {
        return {
          bannerFound: false,
          hasAcceptButton: false,
          hasRejectButton: false,
          hasGranularOptions: false,
          hasPrivacyPolicyLink: false,
          preTickedBoxes: false,
        };
      }

      const bannerText = bannerElement.textContent?.trim() || '';
      
      // Check for buttons
      const buttons = bannerElement.querySelectorAll('button, a, input[type="button"], input[type="submit"]');
      let hasAcceptButton = false;
      let hasRejectButton = false;
      
      buttons.forEach(button => {
        const buttonText = button.textContent?.toLowerCase() || '';
        const buttonValue = (button as HTMLInputElement).value?.toLowerCase() || '';
        const combinedText = buttonText + ' ' + buttonValue;
        
        if (combinedText.includes('accept') || combinedText.includes('agree') || combinedText.includes('allow')) {
          hasAcceptButton = true;
        }
        if (combinedText.includes('reject') || combinedText.includes('decline') || combinedText.includes('deny')) {
          hasRejectButton = true;
        }
      });

      // Check for granular options (checkboxes, toggles)
      const checkboxes = bannerElement.querySelectorAll('input[type="checkbox"], input[type="radio"], [role="switch"]');
      const hasGranularOptions = checkboxes.length > 1; // More than just accept/reject

      // Check for privacy policy links
      const links = bannerElement.querySelectorAll('a[href]');
      let hasPrivacyPolicyLink = false;
      links.forEach(link => {
        const linkText = link.textContent?.toLowerCase() || '';
        const href = (link as HTMLAnchorElement).href.toLowerCase();
        if (linkText.includes('privacy') || linkText.includes('policy') || 
            href.includes('privacy') || href.includes('policy')) {
          hasPrivacyPolicyLink = true;
        }
      });

      // Check for pre-ticked boxes
      const preTickedBoxes = Array.from(checkboxes).some(checkbox => 
        (checkbox as HTMLInputElement).checked
      );

      return {
        bannerFound: true,
        bannerText,
        hasAcceptButton,
        hasRejectButton,
        hasGranularOptions,
        hasPrivacyPolicyLink,
        location: bannerElement.tagName.toLowerCase(),
        preTickedBoxes,
      };
    });
  }

  /**
   * Evaluate banner compliance and generate score
   */
  private evaluateBannerCompliance(analysis: {
    hasAcceptButton: boolean;
    hasRejectButton: boolean;
    hasPrivacyPolicyLink: boolean;
    hasGranularOptions: boolean;
    preTickedBoxes: boolean;
  }): {
    score: number;
    evidence: Evidence[];
  } {
    const evidence: Evidence[] = [];
    let score = 0;

    // Accept button (required) - 25 points
    if (analysis.hasAcceptButton) {
      score += 25;
      evidence.push({
        type: 'element',
        description: 'Accept button found',
        value: 'Compliant',
      });
    } else {
      evidence.push({
        type: 'element',
        description: 'Accept button missing',
        value: 'Non-compliant',
      });
    }

    // Reject button (required) - 25 points
    if (analysis.hasRejectButton) {
      score += 25;
      evidence.push({
        type: 'element',
        description: 'Reject button found',
        value: 'Compliant',
      });
    } else {
      evidence.push({
        type: 'element',
        description: 'Reject button missing',
        value: 'Non-compliant',
      });
    }

    // Privacy policy link (recommended) - 20 points
    if (analysis.hasPrivacyPolicyLink) {
      score += 20;
      evidence.push({
        type: 'element',
        description: 'Privacy policy link found',
        value: 'Compliant',
      });
    } else {
      evidence.push({
        type: 'element',
        description: 'Privacy policy link missing',
        value: 'Improvement needed',
      });
    }

    // Granular options (recommended) - 20 points
    if (analysis.hasGranularOptions) {
      score += 20;
      evidence.push({
        type: 'element',
        description: 'Granular consent options found',
        value: 'Compliant',
      });
    } else {
      evidence.push({
        type: 'element',
        description: 'Granular consent options missing',
        value: 'Improvement recommended',
      });
    }

    // No pre-ticked boxes (required) - 10 points
    if (!analysis.preTickedBoxes) {
      score += 10;
      evidence.push({
        type: 'element',
        description: 'No pre-ticked consent boxes',
        value: 'Compliant',
      });
    } else {
      evidence.push({
        type: 'element',
        description: 'Pre-ticked consent boxes found',
        value: 'Non-compliant - violates GDPR',
      });
    }

    return { score, evidence };
  }

  /**
   * Test consent functionality - REAL interaction testing
   */
  private async testConsentFunctionality(page: Page, analysis: {
    hasAcceptButton: boolean;
    hasRejectButton: boolean;
  }): Promise<{
    working: boolean;
    evidence: Evidence[];
  }> {
    const evidence: Evidence[] = [];
    let working = true;

    try {
      // Try to interact with accept button if present
      if (analysis.hasAcceptButton) {
        const acceptClicked = await page.evaluate(() => {
          const buttons = document.querySelectorAll('button, a, input[type="button"]');
          for (const button of buttons) {
            const text = button.textContent?.toLowerCase() || '';
            if (text.includes('accept') || text.includes('agree')) {
              (button as HTMLElement).click();
              return true;
            }
          }
          return false;
        });

        if (acceptClicked) {
          evidence.push({
            type: 'element',
            description: 'Accept button functional',
            value: 'Successfully clicked',
          });
        } else {
          working = false;
          evidence.push({
            type: 'element',
            description: 'Accept button not functional',
            value: 'Click failed',
          });
        }
      }

    } catch (error) {
      working = false;
      evidence.push({
        type: 'element',
        description: 'Consent functionality test failed',
        value: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return { working, evidence };
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(analysis: {
    hasRejectButton: boolean;
    hasAcceptButton: boolean;
  }, score: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (score < 70) {
      recommendations.push({
        priority: 1,
        title: 'Improve cookie consent banner',
        description: 'Ensure banner includes both accept and reject options with clear labeling',
        implementation: 'Add missing buttons and ensure GDPR compliance',
        effort: 'moderate',
        impact: 'high',
      });
    }

    if (!analysis.hasRejectButton) {
      recommendations.push({
        priority: 2,
        title: 'Add reject button',
        description: 'Include a clear reject/decline option for users',
        implementation: 'Add reject button with equal prominence to accept button',
        effort: 'minimal',
        impact: 'high',
      });
    }

    return recommendations;
  }
}
