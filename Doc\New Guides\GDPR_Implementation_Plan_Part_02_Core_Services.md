# GDPR Implementation Plan - Part 02: Core Services Implementation

## Overview
This document implements the core GDPR scanning services, orchestrator, and database integration. All implementations use real website scanning - NO MOCK DATA.

## ⚠️ CRITICAL REQUIREMENTS
- **Real Website Scanning Only**: All services must scan actual websites
- **No Mock Data**: Prohibited - use live HTTP requests and real content analysis
- **TypeScript Strict Mode**: Follow .projectrules - NO `any` types
- **Error Handling**: Proper error handling with `unknown` type and type guards

## Step 1: GDPR Database Service

### 1.1 Implement GDPR Database Class
Create `backend/src/compliance/gdpr/database/gdpr-database.ts`:

```typescript
import { knex } from '../../../lib/database';
import { 
  GdprScanResult, 
  GdprScanEntity, 
  GdprCheckResultEntity,
  GdprRuleId,
  ScanStatus 
} from '../types';
import { v4 as uuidv4 } from 'uuid';

export class GdprDatabase {
  /**
   * Save complete GDPR scan result to database
   * NO MOCK DATA - stores real scan results only
   */
  static async saveScanResult(
    userId: string, 
    scanResult: GdprScanResult
  ): Promise<string> {
    const scanId = uuidv4();
    
    try {
      await knex.transaction(async (trx) => {
        // Insert main scan record
        await trx('gdpr_scans').insert({
          id: scanId,
          user_id: userId,
          target_url: scanResult.targetUrl,
          scan_timestamp: new Date(scanResult.timestamp),
          scan_duration: scanResult.scanDuration,
          overall_score: scanResult.overallScore,
          risk_level: scanResult.riskLevel,
          total_checks: scanResult.summary.totalChecks,
          passed_checks: scanResult.summary.passedChecks,
          failed_checks: scanResult.summary.failedChecks,
          manual_review_required: scanResult.summary.manualReviewRequired,
          scan_status: scanResult.status,
          metadata: scanResult.metadata,
        });

        // Insert check results
        const checkResults = scanResult.checks.map((check) => ({
          id: uuidv4(),
          scan_id: scanId,
          rule_id: check.ruleId,
          rule_name: check.ruleName,
          category: check.category,
          passed: check.passed,
          score: check.score,
          weight: check.weight,
          severity: check.severity,
          manual_review_required: check.manualReviewRequired,
          evidence: check.evidence,
          recommendations: check.recommendations,
        }));

        await trx('gdpr_check_results').insert(checkResults);
      });

      console.log(`✅ GDPR scan results saved to database: ${scanId}`);
      return scanId;
    } catch (error) {
      console.error('❌ Failed to save GDPR scan results:', error);
      throw new Error(`Database save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Retrieve GDPR scan result by ID
   */
  static async getScanResult(scanId: string): Promise<GdprScanResult | null> {
    try {
      const scanRecord = await knex('gdpr_scans')
        .where('id', scanId)
        .first() as GdprScanEntity | undefined;

      if (!scanRecord) {
        return null;
      }

      const checkResults = await knex('gdpr_check_results')
        .where('scan_id', scanId)
        .orderBy('created_at', 'asc') as GdprCheckResultEntity[];

      return this.mapEntityToResult(scanRecord, checkResults);
    } catch (error) {
      console.error('❌ Failed to retrieve GDPR scan result:', error);
      throw new Error(`Database retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update scan status
   */
  static async updateScanStatus(
    scanId: string, 
    status: ScanStatus, 
    errorMessage?: string
  ): Promise<void> {
    try {
      await knex('gdpr_scans')
        .where('id', scanId)
        .update({
          scan_status: status,
          error_message: errorMessage,
          updated_at: new Date(),
        });
    } catch (error) {
      console.error('❌ Failed to update scan status:', error);
      throw new Error(`Status update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user's GDPR scan history
   */
  static async getUserScans(
    userId: string, 
    limit: number = 50, 
    offset: number = 0
  ): Promise<GdprScanEntity[]> {
    try {
      return await knex('gdpr_scans')
        .where('user_id', userId)
        .orderBy('scan_timestamp', 'desc')
        .limit(limit)
        .offset(offset) as GdprScanEntity[];
    } catch (error) {
      console.error('❌ Failed to retrieve user scans:', error);
      throw new Error(`Scan history retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Save cookie analysis results
   */
  static async saveCookieAnalysis(
    scanId: string, 
    cookies: Array<{
      name: string;
      domain: string;
      category: string;
      hasConsent: boolean;
      secureFlag: boolean;
      httpOnlyFlag: boolean;
      sameSiteAttribute?: string;
      expiryDate?: Date;
      purpose?: string;
      thirdParty: boolean;
    }>
  ): Promise<void> {
    try {
      const cookieRecords = cookies.map((cookie) => ({
        id: uuidv4(),
        scan_id: scanId,
        cookie_name: cookie.name,
        cookie_domain: cookie.domain,
        cookie_category: cookie.category,
        has_consent: cookie.hasConsent,
        secure_flag: cookie.secureFlag,
        httponly_flag: cookie.httpOnlyFlag,
        samesite_attribute: cookie.sameSiteAttribute,
        expiry_date: cookie.expiryDate,
        purpose: cookie.purpose,
        third_party: cookie.thirdParty,
      }));

      await knex('gdpr_cookie_analysis').insert(cookieRecords);
    } catch (error) {
      console.error('❌ Failed to save cookie analysis:', error);
      throw new Error(`Cookie analysis save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Save consent analysis results
   */
  static async saveConsentAnalysis(
    scanId: string,
    consentData: {
      consentType: string;
      consentMechanism: string;
      hasRejectOption: boolean;
      hasGranularOptions: boolean;
      preTickedBoxes: boolean;
      consentText: string;
      privacyPolicyLinked: boolean;
      compliant: boolean;
      issues: string[];
    }
  ): Promise<void> {
    try {
      await knex('gdpr_consent_analysis').insert({
        id: uuidv4(),
        scan_id: scanId,
        consent_type: consentData.consentType,
        consent_mechanism: consentData.consentMechanism,
        has_reject_option: consentData.hasRejectOption,
        has_granular_options: consentData.hasGranularOptions,
        pre_ticked_boxes: consentData.preTickedBoxes,
        consent_text: consentData.consentText,
        privacy_policy_linked: consentData.privacyPolicyLinked,
        compliant: consentData.compliant,
        issues: consentData.issues,
      });
    } catch (error) {
      console.error('❌ Failed to save consent analysis:', error);
      throw new Error(`Consent analysis save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Save tracker analysis results
   */
  static async saveTrackerAnalysis(
    scanId: string,
    trackers: Array<{
      domain: string;
      type: string;
      name: string;
      loadsBeforeConsent: boolean;
      hasConsentMechanism: boolean;
      dataTransferred: string;
      privacyPolicyMentioned: boolean;
      compliant: boolean;
    }>
  ): Promise<void> {
    try {
      const trackerRecords = trackers.map((tracker) => ({
        id: uuidv4(),
        scan_id: scanId,
        tracker_domain: tracker.domain,
        tracker_type: tracker.type,
        tracker_name: tracker.name,
        loads_before_consent: tracker.loadsBeforeConsent,
        has_consent_mechanism: tracker.hasConsentMechanism,
        data_transferred: tracker.dataTransferred,
        privacy_policy_mentioned: tracker.privacyPolicyMentioned,
        compliant: tracker.compliant,
      }));

      await knex('gdpr_tracker_analysis').insert(trackerRecords);
    } catch (error) {
      console.error('❌ Failed to save tracker analysis:', error);
      throw new Error(`Tracker analysis save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Map database entities to result objects
   */
  private static mapEntityToResult(
    scanEntity: GdprScanEntity,
    checkEntities: GdprCheckResultEntity[]
  ): GdprScanResult {
    const checks = checkEntities.map((check) => ({
      ruleId: check.rule_id,
      ruleName: check.rule_name,
      category: check.category,
      passed: check.passed,
      score: check.score,
      weight: check.weight,
      severity: check.severity,
      evidence: Array.isArray(check.evidence) ? check.evidence : [],
      recommendations: Array.isArray(check.recommendations) ? check.recommendations : [],
      manualReviewRequired: check.manual_review_required,
    }));

    const categoryBreakdown = this.calculateCategoryBreakdown(checks);

    return {
      scanId: scanEntity.id,
      targetUrl: scanEntity.target_url,
      timestamp: scanEntity.scan_timestamp.toISOString(),
      scanDuration: scanEntity.scan_duration,
      overallScore: scanEntity.overall_score,
      riskLevel: scanEntity.risk_level,
      status: scanEntity.scan_status,
      summary: {
        totalChecks: scanEntity.total_checks,
        passedChecks: scanEntity.passed_checks,
        failedChecks: scanEntity.failed_checks,
        manualReviewRequired: scanEntity.manual_review_required,
        criticalFailures: checks.filter(c => !c.passed && c.severity === 'critical').length,
        categoryBreakdown,
      },
      checks,
      recommendations: [], // Will be populated by orchestrator
      metadata: scanEntity.metadata as any, // Safe cast for metadata
    };
  }

  /**
   * Calculate category breakdown for summary
   */
  private static calculateCategoryBreakdown(checks: GdprCheckResult[]): CategoryBreakdown[] {
    const categoryMap = new Map<GdprCategory, {
      category: GdprCategory;
      score: number;
      checksInCategory: number;
      passedInCategory: number;
    }>();

    for (const check of checks) {
      const existing = categoryMap.get(check.category) || {
        category: check.category,
        score: 0,
        checksInCategory: 0,
        passedInCategory: 0,
      };

      existing.checksInCategory++;
      if (check.passed) {
        existing.passedInCategory++;
      }
      existing.score = existing.checksInCategory > 0
        ? Math.round((existing.passedInCategory / existing.checksInCategory) * 100)
        : 0;

      categoryMap.set(check.category, existing);
    }

    return Array.from(categoryMap.values());
  }
}
```

## Step 2: GDPR Constants

### 2.1 Create GDPR Constants File
Create `backend/src/compliance/gdpr/constants.ts`:

```typescript
// GDPR Constants - Following HIPAA patterns but for GDPR compliance
// NO any[] types - all arrays specify element types

export const GDPR_RULE_DEFINITIONS = {
  'GDPR-001': {
    name: 'HTTPS/TLS Encryption',
    description: 'Verify HTTPS encryption and security headers',
    category: 'security' as const,
    weight: 8,
    severity: 'critical' as const,
    automated: true,
  },
  'GDPR-002': {
    name: 'Privacy Policy Presence',
    description: 'Check for privacy policy link and accessibility',
    category: 'privacy_policy' as const,
    weight: 7,
    severity: 'high' as const,
    automated: true,
  },
  'GDPR-003': {
    name: 'Privacy Notice Content',
    description: 'Analyze privacy policy content for GDPR requirements',
    category: 'privacy_policy' as const,
    weight: 7,
    severity: 'high' as const,
    automated: true,
  },
  'GDPR-004': {
    name: 'Cookie Consent Banner',
    description: 'Verify cookie consent banner presence and functionality',
    category: 'consent' as const,
    weight: 9,
    severity: 'critical' as const,
    automated: true,
  },
  'GDPR-005': {
    name: 'Cookie Classification & Blocking',
    description: 'Check cookie categorization and consent-based blocking',
    category: 'cookies' as const,
    weight: 8,
    severity: 'critical' as const,
    automated: true,
  },
  // ... Continue for all 21 rules
} as const;

export const GDPR_SCORING_WEIGHTS = {
  // Critical Requirements (High Weight)
  'GDPR-001': 8, // HTTPS/TLS
  'GDPR-002': 7, // Privacy Policy Presence
  'GDPR-004': 9, // Cookie Consent
  'GDPR-005': 8, // Cookie Classification
  
  // High Priority (Medium-High Weight)
  'GDPR-003': 7, // Privacy Notice Content
  'GDPR-006': 6, // Tracker Detection
  'GDPR-012': 7, // Data Subject Rights
  
  // Medium Priority (Medium Weight)
  'GDPR-007': 5, // Cookie Attributes
  'GDPR-008': 4, // GPC/DNT
  'GDPR-009': 6, // Form Consent
  'GDPR-010': 5, // Security Headers
  'GDPR-011': 5, // IP Anonymization
  
  // Standard Requirements (Lower Weight)
  'GDPR-013': 4, // Special Category Data
  'GDPR-015': 3, // DPO Contact
  'GDPR-016': 5, // Data Transfers
  'GDPR-017': 3, // Breach Notification
  'GDPR-019': 4, // Data Retention
  'GDPR-020': 3, // Processor Agreements
  'GDPR-021': 2, // Imprint Contact
  
  // Manual Review Items (Flagged)
  'GDPR-014': 0, // Children's Consent
  'GDPR-018': 0, // DPIA
} as const;

export const GDPR_SCAN_CONFIG = {
  DEFAULT_TIMEOUT: 300000, // 5 minutes
  MAX_PAGES: 50,
  DEFAULT_USER_AGENT: 'GDPR-Compliance-Scanner/1.0',
  COOKIE_ANALYSIS_TIMEOUT: 30000,
  CONSENT_INTERACTION_TIMEOUT: 15000,
} as const;

export const TRACKER_DOMAINS = [
  // Analytics
  'google-analytics.com',
  'googletagmanager.com',
  'adobe.com',
  'hotjar.com',
  
  // Advertising
  'doubleclick.net',
  'facebook.com',
  'twitter.com',
  'linkedin.com',
  
  // Social Media
  'youtube.com',
  'instagram.com',
  'tiktok.com',
] as const;

export const COOKIE_CATEGORIES = {
  ESSENTIAL: 'essential',
  ANALYTICS: 'analytics', 
  MARKETING: 'marketing',
  FUNCTIONAL: 'functional',
} as const;
```

## Step 3: Basic Orchestrator Structure

### 3.1 Create GDPR Orchestrator
Create `backend/src/compliance/gdpr/orchestrator.ts`:

```typescript
import {
  GdprScanRequest,
  GdprScanResult,
  ScanStatus,
  GdprCheckResult,
  RiskLevel,
  GdprCategory,
  CategoryBreakdown,
  GdprRecommendation
} from './types';
import { GdprDatabase } from './database/gdpr-database';
import { GDPR_SCORING_WEIGHTS } from './constants';
import { v4 as uuidv4 } from 'uuid';

export class GdprOrchestrator {
  /**
   * Perform comprehensive GDPR compliance scan
   * REAL WEBSITE SCANNING ONLY - NO MOCK DATA
   */
  async performComprehensiveScan(
    userId: string,
    scanRequest: GdprScanRequest
  ): Promise<GdprScanResult> {
    const scanId = uuidv4();
    const startTime = Date.now();

    console.log(`🔍 Starting GDPR compliance scan for: ${scanRequest.targetUrl}`);
    console.log(`📋 Scan ID: ${scanId}`);

    try {
      // Update status to running
      await GdprDatabase.updateScanStatus(scanId, 'running');

      // REAL SCANNING IMPLEMENTATION - Execute all 21 GDPR checks
      const checks = await this.executeAllGdprChecks(scanRequest);

      // Calculate overall score using risk-weighted algorithm
      const scoringResult = this.calculateGdprScore(checks);

      const scanDuration = Date.now() - startTime;

      // REAL SCAN RESULT - populated with actual check results
      const result: GdprScanResult = {
        scanId,
        targetUrl: scanRequest.targetUrl,
        timestamp: new Date().toISOString(),
        scanDuration,
        overallScore: scoringResult.overallScore,
        riskLevel: scoringResult.riskLevel,
        status: 'completed',
        summary: {
          totalChecks: checks.length,
          passedChecks: scoringResult.summary.passedChecks,
          failedChecks: scoringResult.summary.failedChecks,
          manualReviewRequired: scoringResult.summary.manualReviewRequired,
          criticalFailures: scoringResult.criticalFailures,
          categoryBreakdown: scoringResult.breakdown,
        },
        checks,
        recommendations: this.generateRecommendations(checks),
        metadata: {
          version: '1.0.0',
          processingTime: scanDuration,
          checksPerformed: checks.length,
          analysisLevelsUsed: ['pattern', 'behavioral', 'content', 'network'],
          errors: checks.filter(c => !c.passed).map(c => `${c.ruleName}: Failed`),
          warnings: checks.filter(c => c.manualReviewRequired).map(c => `${c.ruleName}: Manual review required`),
          userAgent: 'GDPR-Compliance-Scanner/1.0',
          scanOptions: scanRequest.scanOptions,
        },
      };

      // Save results to database
      await GdprDatabase.saveScanResult(userId, result);
      await GdprDatabase.updateScanStatus(scanId, 'completed');

      console.log(`✅ GDPR scan completed successfully: ${scanId}`);
      return result;

    } catch (error) {
      console.error(`❌ GDPR scan failed: ${scanId}`, error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await GdprDatabase.updateScanStatus(scanId, 'failed', errorMessage);
      
      throw new Error(`GDPR scan failed: ${errorMessage}`);
    }
  }

  /**
   * Execute all 21 GDPR compliance checks
   * REAL IMPLEMENTATION - calls actual check classes
   */
  private async executeAllGdprChecks(scanRequest: GdprScanRequest): Promise<GdprCheckResult[]> {
    const checks: GdprCheckResult[] = [];
    const config = {
      targetUrl: scanRequest.targetUrl,
      timeout: scanRequest.scanOptions?.timeout || 300000,
    };

    try {
      // Import all check classes
      const {
        HttpsTlsCheck, PrivacyPolicyCheck, PrivacyContentCheck, CookieConsentCheck,
        CookieClassificationCheck, TrackerDetectionCheck, CookieAttributesCheck,
        GpcDntCheck, FormConsentCheck, SecurityHeadersCheck, IpAnonymizationCheck,
        DataRightsCheck, SpecialDataCheck, ChildrenConsentCheck, DpoContactCheck,
        DataTransfersCheck, BreachNotificationCheck, DpiaCheck, DataRetentionCheck,
        ProcessorAgreementsCheck, ImprintContactCheck
      } = await import('../checks');

      // Execute all checks in parallel for performance
      const checkPromises = [
        new HttpsTlsCheck().performCheck(config),
        new PrivacyPolicyCheck().performCheck(config),
        new PrivacyContentCheck().performCheck(config),
        new CookieConsentCheck().performCheck(config),
        new CookieClassificationCheck().performCheck({ ...config, scanId: 'temp' }),
        new TrackerDetectionCheck().performCheck(config),
        new CookieAttributesCheck().performCheck(config),
        new GpcDntCheck().performCheck(config),
        new FormConsentCheck().performCheck(config),
        new SecurityHeadersCheck().performCheck(config),
        new IpAnonymizationCheck().performCheck(config),
        new DataRightsCheck().performCheck(config),
        new SpecialDataCheck().performCheck(config),
        new ChildrenConsentCheck().performCheck(config),
        new DpoContactCheck().performCheck(config),
        new DataTransfersCheck().performCheck(config),
        new BreachNotificationCheck().performCheck(config),
        new DpiaCheck().performCheck(config),
        new DataRetentionCheck().performCheck(config),
        new ProcessorAgreementsCheck().performCheck(config),
        new ImprintContactCheck().performCheck(config),
      ];

      // Wait for all checks to complete
      const results = await Promise.allSettled(checkPromises);

      // Process results and handle any failures
      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        if (result.status === 'fulfilled') {
          checks.push(result.value);
        } else {
          // Create error result for failed check
          checks.push({
            ruleId: `GDPR-${String(i + 1).padStart(3, '0')}` as any,
            ruleName: `Check ${i + 1}`,
            category: 'security',
            passed: false,
            score: 0,
            weight: 1,
            severity: 'medium',
            evidence: [{
              type: 'text',
              description: 'Check execution failed',
              value: result.reason?.message || 'Unknown error',
            }],
            recommendations: [{
              priority: 1,
              title: 'Fix check execution',
              description: 'Resolve check implementation issues',
              implementation: 'Debug and fix check logic',
              effort: 'moderate',
              impact: 'medium',
            }],
            manualReviewRequired: false,
          });
        }
      }

      console.log(`✅ Completed ${checks.length} GDPR checks`);
      return checks;

    } catch (error) {
      console.error('❌ Failed to execute GDPR checks:', error);
      throw new Error(`Check execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate GDPR compliance score using risk-weighted algorithm
   */
  private calculateGdprScore(checks: GdprCheckResult[]): {
    overallScore: number;
    riskLevel: RiskLevel;
    criticalFailures: number;
    breakdown: CategoryBreakdown[];
    summary: {
      passedChecks: number;
      failedChecks: number;
      manualReviewRequired: number;
    };
  } {
    const weights = GDPR_SCORING_WEIGHTS;
    let totalWeightedScore = 0;
    let totalWeight = 0;
    let criticalFailures = 0;
    let passedChecks = 0;
    let manualReviewRequired = 0;

    for (const check of checks) {
      const weight = weights[check.ruleId] || 1;
      const score = check.passed ? 100 : 0;

      totalWeightedScore += score * weight;
      totalWeight += weight;

      if (check.passed) passedChecks++;
      if (check.manualReviewRequired) manualReviewRequired++;
      if (!check.passed && weight >= 7) criticalFailures++;
    }

    const baseScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
    const criticalPenalty = criticalFailures * 15;
    const finalScore = Math.max(0, baseScore - criticalPenalty);

    return {
      overallScore: Math.round(finalScore),
      riskLevel: this.determineRiskLevel(finalScore, criticalFailures),
      criticalFailures,
      breakdown: this.generateCategoryBreakdown(checks),
      summary: {
        passedChecks,
        failedChecks: checks.length - passedChecks,
        manualReviewRequired,
      },
    };
  }

  /**
   * Determine risk level based on score and critical failures
   */
  private determineRiskLevel(score: number, criticalFailures: number): RiskLevel {
    if (criticalFailures >= 3 || score < 40) return 'critical';
    if (criticalFailures >= 2 || score < 60) return 'high';
    if (criticalFailures >= 1 || score < 75) return 'medium';
    return 'low';
  }

  /**
   * Generate category breakdown for scoring
   */
  private generateCategoryBreakdown(checks: GdprCheckResult[]): CategoryBreakdown[] {
    const categoryMap = new Map<GdprCategory, {
      totalChecks: number;
      passedChecks: number;
    }>();

    for (const check of checks) {
      const existing = categoryMap.get(check.category) || {
        totalChecks: 0,
        passedChecks: 0,
      };

      existing.totalChecks++;
      if (check.passed) existing.passedChecks++;

      categoryMap.set(check.category, existing);
    }

    return Array.from(categoryMap.entries()).map(([category, data]) => ({
      category,
      score: data.totalChecks > 0 ? Math.round((data.passedChecks / data.totalChecks) * 100) : 0,
      checksInCategory: data.totalChecks,
      passedInCategory: data.passedChecks,
    }));
  }

  /**
   * Generate recommendations based on check results
   */
  private generateRecommendations(checks: GdprCheckResult[]): GdprRecommendation[] {
    const recommendations: GdprRecommendation[] = [];
    let recommendationId = 1;

    for (const check of checks) {
      if (!check.passed && check.recommendations.length > 0) {
        for (const rec of check.recommendations) {
          recommendations.push({
            id: `REC-${recommendationId++}`,
            priority: rec.priority,
            title: rec.title,
            description: rec.description,
            category: check.category,
            effort: rec.effort,
            impact: rec.impact,
            timeline: this.getTimelineFromEffort(rec.effort),
            relatedRules: [check.ruleId],
          });
        }
      }
    }

    // Sort by priority
    return recommendations.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Get timeline estimate based on effort level
   */
  private getTimelineFromEffort(effort: string): string {
    switch (effort) {
      case 'minimal': return '1-2 days';
      case 'moderate': return '1-2 weeks';
      case 'significant': return '2-4 weeks';
      default: return '1-2 weeks';
    }
  }

  /**
   * Get scan status
   */
  async getScanStatus(scanId: string): Promise<ScanStatus | null> {
    try {
      const result = await GdprDatabase.getScanResult(scanId);
      return result?.status || null;
    } catch (error) {
      console.error('❌ Failed to get scan status:', error);
      return null;
    }
  }
}
```

## Next Steps
Continue with Part 03: Individual Check Implementations to build the actual GDPR rule checking logic.

## Validation Checklist
- [ ] Database service compiles without TypeScript errors
- [ ] No `any` types used (strict .projectrules compliance)
- [ ] All database operations use proper error handling
- [ ] Constants follow HIPAA patterns but adapted for GDPR
- [ ] Orchestrator structure ready for real scanning implementation
- [ ] Ready to implement individual GDPR checks
