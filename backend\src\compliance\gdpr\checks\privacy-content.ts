import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface PrivacyContentCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class PrivacyContentCheck {
  /**
   * Analyze privacy policy content for GDPR requirements
   * REAL ANALYSIS - analyzes actual privacy policy text
   */
  async performCheck(config: PrivacyContentCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Find privacy policy links
      const privacyLinks = await this.findPrivacyPolicyLinks(page);
      
      if (privacyLinks.length === 0) {
        evidence.push({
          type: 'text',
          description: 'No privacy policy found for content analysis',
          value: 'Cannot analyze privacy notice content',
        });
        
        return this.createFailedResult(evidence, 'No privacy policy found');
      }

      // Analyze the first privacy policy found
      const privacyUrl = privacyLinks[0].href;
      await page.goto(privacyUrl, { waitUntil: 'networkidle2', timeout: config.timeout });

      // Extract privacy policy text
      const privacyText = await page.evaluate(() => {
        return document.body.textContent || '';
      });

      // Analyze GDPR content requirements
      const contentAnalysis = this.analyzeGdprContent(privacyText);
      
      // Calculate score based on required elements found
      const totalRequirements = 10; // Key GDPR requirements
      const foundRequirements = contentAnalysis.foundElements.length;
      score = Math.round((foundRequirements / totalRequirements) * 100);

      // Add evidence for found elements
      for (const element of contentAnalysis.foundElements) {
        evidence.push({
          type: 'text',
          description: `GDPR requirement found: ${element.name}`,
          value: element.evidence,
          location: 'Privacy policy',
        });
      }

      // Add evidence for missing elements
      for (const missing of contentAnalysis.missingElements) {
        evidence.push({
          type: 'text',
          description: `Missing GDPR requirement: ${missing}`,
          value: 'Required by GDPR Article 13/14',
        });
      }

      const passed = score >= 70;

      return {
        ruleId: 'GDPR-003',
        ruleName: 'Privacy Notice Content',
        category: 'privacy_policy',
        passed,
        score,
        weight: 7,
        severity: 'high',
        evidence,
        recommendations: this.generateContentRecommendations(contentAnalysis),
        manualReviewRequired: true, // Legal adequacy requires manual review
      };

    } catch (error) {
      return this.createFailedResult([{
        type: 'text',
        description: 'Privacy content analysis failed',
        value: error instanceof Error ? error.message : 'Unknown error',
      }], 'Analysis failed');
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Find privacy policy links on the page
   */
  private async findPrivacyPolicyLinks(page: Page): Promise<Array<{
    text: string;
    href: string;
  }>> {
    return await page.evaluate(() => {
      const links: Array<{ text: string; href: string }> = [];
      const privacyPatterns = [
        /privacy\s*policy/i,
        /privacy\s*notice/i,
        /privacy\s*statement/i,
        /data\s*protection/i,
      ];

      const allLinks = document.querySelectorAll('a[href]');
      
      allLinks.forEach((link) => {
        const text = link.textContent?.trim() || '';
        const href = (link as HTMLAnchorElement).href;
        
        const matchesPattern = privacyPatterns.some(pattern => 
          pattern.test(text) || pattern.test(href)
        );
        
        if (matchesPattern) {
          links.push({ text, href });
        }
      });

      return links;
    });
  }

  /**
   * Analyze privacy policy content for GDPR requirements
   */
  private analyzeGdprContent(text: string): {
    foundElements: Array<{ name: string; evidence: string }>;
    missingElements: string[];
  } {
    const lowerText = text.toLowerCase();
    const foundElements: Array<{ name: string; evidence: string }> = [];
    const missingElements: string[] = [];

    const gdprRequirements = [
      {
        name: 'Controller Identity',
        patterns: ['controller', 'data controller', 'company name', 'organization'],
        required: true,
      },
      {
        name: 'Contact Information',
        patterns: ['contact', 'email', 'address', 'phone'],
        required: true,
      },
      {
        name: 'Processing Purposes',
        patterns: ['purpose', 'why we collect', 'use of data', 'processing'],
        required: true,
      },
      {
        name: 'Legal Basis',
        patterns: ['legal basis', 'lawful basis', 'consent', 'legitimate interest'],
        required: true,
      },
      {
        name: 'Data Categories',
        patterns: ['personal data', 'information we collect', 'data types'],
        required: true,
      },
      {
        name: 'Recipients',
        patterns: ['share', 'third party', 'recipients', 'disclosure'],
        required: true,
      },
      {
        name: 'Retention Periods',
        patterns: ['retention', 'how long', 'storage period', 'delete'],
        required: true,
      },
      {
        name: 'Data Subject Rights',
        patterns: ['your rights', 'data subject', 'access', 'rectification'],
        required: true,
      },
      {
        name: 'International Transfers',
        patterns: ['transfer', 'international', 'third country', 'outside'],
        required: false,
      },
      {
        name: 'DPO Contact',
        patterns: ['data protection officer', 'dpo', 'privacy officer'],
        required: false,
      },
    ];

    for (const requirement of gdprRequirements) {
      const found = requirement.patterns.some(pattern => lowerText.includes(pattern));
      
      if (found) {
        // Find evidence text
        const patternFound = requirement.patterns.find(pattern => lowerText.includes(pattern));
        const index = lowerText.indexOf(patternFound!);
        const evidence = text.substring(Math.max(0, index - 50), index + 100);
        
        foundElements.push({
          name: requirement.name,
          evidence: evidence.trim(),
        });
      } else if (requirement.required) {
        missingElements.push(requirement.name);
      }
    }

    return { foundElements, missingElements };
  }

  /**
   * Generate content-specific recommendations
   */
  private generateContentRecommendations(analysis: {
    missingElements: string[];
  }): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (analysis.missingElements.length > 0) {
      recommendations.push({
        priority: 1,
        title: 'Add missing GDPR content requirements',
        description: `Privacy policy missing: ${analysis.missingElements.join(', ')}`,
        implementation: 'Update privacy policy to include all required GDPR elements',
        effort: 'significant',
        impact: 'high',
      });
    }

    recommendations.push({
      priority: 2,
      title: 'Manual legal review required',
      description: 'Privacy policy content requires legal adequacy assessment',
      implementation: 'Have legal expert review privacy policy for GDPR compliance',
      effort: 'moderate',
      impact: 'high',
    });

    return recommendations;
  }

  /**
   * Create failed result
   */
  private createFailedResult(evidence: Evidence[], reason: string): GdprCheckResult {
    return {
      ruleId: 'GDPR-003',
      ruleName: 'Privacy Notice Content',
      category: 'privacy_policy',
      passed: false,
      score: 0,
      weight: 7,
      severity: 'high',
      evidence,
      recommendations: [{
        priority: 1,
        title: 'Create comprehensive privacy policy',
        description: `${reason} - implement GDPR-compliant privacy notice`,
        implementation: 'Create privacy policy with all required GDPR elements',
        effort: 'significant',
        impact: 'high',
      }],
      manualReviewRequired: true,
    };
  }
}
