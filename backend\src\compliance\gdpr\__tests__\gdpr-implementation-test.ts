/**
 * GDPR Implementation Test
 * 
 * Basic test to verify GDPR Part 3 implementation works correctly.
 * Tests the core implemented checks: HTTPS/TLS, Privacy Policy, <PERSON>ie <PERSON>, Security Headers, Data Rights.
 */

import { GdprOrchestrator } from '../orchestrator';
import { GdprScanRequest } from '../types';

describe('GDPR Part 3 Implementation', () => {
  let orchestrator: GdprOrchestrator;

  beforeEach(() => {
    orchestrator = new GdprOrchestrator();
  });

  describe('Individual Check Implementations', () => {
    it('should import all check classes without errors', async () => {
      const {
        HttpsTlsCheck,
        PrivacyPolicyCheck,
        CookieConsentCheck,
        SecurityHeadersCheck,
        DataRightsCheck,
      } = await import('../checks');

      expect(HttpsTlsCheck).toBeDefined();
      expect(PrivacyPolicyCheck).toBeDefined();
      expect(CookieConsentCheck).toBeDefined();
      expect(SecurityHeadersCheck).toBeDefined();
      expect(DataRightsCheck).toBeDefined();
    });

    it('should create check instances successfully', async () => {
      const {
        HttpsTlsCheck,
        PrivacyPolicyCheck,
        CookieConsentCheck,
        SecurityHeadersCheck,
        DataRightsCheck,
      } = await import('../checks');

      expect(() => new HttpsTlsCheck()).not.toThrow();
      expect(() => new PrivacyPolicyCheck()).not.toThrow();
      expect(() => new CookieConsentCheck()).not.toThrow();
      expect(() => new SecurityHeadersCheck()).not.toThrow();
      expect(() => new DataRightsCheck()).not.toThrow();
    });

    it('should have correct rule IDs for implemented checks', async () => {
      const {
        HttpsTlsCheck,
        PrivacyPolicyCheck,
        CookieConsentCheck,
        SecurityHeadersCheck,
        DataRightsCheck,
      } = await import('../checks');

      const config = { targetUrl: 'https://example.com', timeout: 30000 };

      // Test HTTPS/TLS Check
      const httpsCheck = new HttpsTlsCheck();
      const httpsResult = await httpsCheck.performCheck(config);
      expect(httpsResult.ruleId).toBe('GDPR-001');
      expect(httpsResult.ruleName).toBe('HTTPS/TLS Encryption');
      expect(httpsResult.category).toBe('security');

      // Test Privacy Policy Check
      const privacyCheck = new PrivacyPolicyCheck();
      const privacyResult = await privacyCheck.performCheck(config);
      expect(privacyResult.ruleId).toBe('GDPR-002');
      expect(privacyResult.ruleName).toBe('Privacy Policy Presence');
      expect(privacyResult.category).toBe('privacy_policy');

      // Test Cookie Consent Check
      const consentCheck = new CookieConsentCheck();
      const consentResult = await consentCheck.performCheck(config);
      expect(consentResult.ruleId).toBe('GDPR-004');
      expect(consentResult.ruleName).toBe('Cookie Consent Banner');
      expect(consentResult.category).toBe('consent');

      // Test Security Headers Check
      const securityCheck = new SecurityHeadersCheck();
      const securityResult = await securityCheck.performCheck(config);
      expect(securityResult.ruleId).toBe('GDPR-010');
      expect(securityResult.ruleName).toBe('Security Headers (Privacy by Design)');
      expect(securityResult.category).toBe('security');

      // Test Data Rights Check
      const rightsCheck = new DataRightsCheck();
      const rightsResult = await rightsCheck.performCheck(config);
      expect(rightsResult.ruleId).toBe('GDPR-013');
      expect(rightsResult.ruleName).toBe('Data Subject Rights');
      expect(rightsResult.category).toBe('data_rights');
    });
  });

  describe('Orchestrator Integration', () => {
    it('should execute all checks without throwing errors', async () => {
      const scanRequest: GdprScanRequest = {
        targetUrl: 'https://example.com',
        scanOptions: {
          timeout: 30000,
          userAgent: 'GDPR-Test-Scanner/1.0',
        },
      };

      // This should not throw an error, even if checks fail
      await expect(
        orchestrator.performComprehensiveScan('test-user-id', scanRequest)
      ).resolves.toBeDefined();
    });

    it('should return proper scan result structure', async () => {
      const scanRequest: GdprScanRequest = {
        targetUrl: 'https://example.com',
        scanOptions: {
          timeout: 30000,
        },
      };

      const result = await orchestrator.performComprehensiveScan('test-user-id', scanRequest);

      // Verify result structure
      expect(result).toHaveProperty('scanId');
      expect(result).toHaveProperty('targetUrl', 'https://example.com');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('scanDuration');
      expect(result).toHaveProperty('overallScore');
      expect(result).toHaveProperty('riskLevel');
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('summary');
      expect(result).toHaveProperty('checks');
      expect(result).toHaveProperty('recommendations');
      expect(result).toHaveProperty('metadata');

      // Verify checks array contains expected number of checks
      expect(Array.isArray(result.checks)).toBe(true);
      expect(result.checks.length).toBe(19); // All 19 GDPR checks (GDPR-001 to GDPR-021, minus 2 missing)

      // Verify summary structure
      expect(result.summary).toHaveProperty('totalChecks');
      expect(result.summary).toHaveProperty('passedChecks');
      expect(result.summary).toHaveProperty('failedChecks');
      expect(result.summary).toHaveProperty('manualReviewRequired');
      expect(result.summary).toHaveProperty('criticalFailures');
      expect(result.summary).toHaveProperty('categoryBreakdown');
    });

    it('should include implemented checks in results', async () => {
      const scanRequest: GdprScanRequest = {
        targetUrl: 'https://example.com',
        scanOptions: {
          timeout: 30000,
        },
      };

      const result = await orchestrator.performComprehensiveScan('test-user-id', scanRequest);

      // Find specific implemented checks
      const httpsCheck = result.checks.find(c => c.ruleId === 'GDPR-001');
      const privacyCheck = result.checks.find(c => c.ruleId === 'GDPR-002');
      const consentCheck = result.checks.find(c => c.ruleId === 'GDPR-004');
      const securityCheck = result.checks.find(c => c.ruleId === 'GDPR-010');
      const rightsCheck = result.checks.find(c => c.ruleId === 'GDPR-013');

      expect(httpsCheck).toBeDefined();
      expect(privacyCheck).toBeDefined();
      expect(consentCheck).toBeDefined();
      expect(securityCheck).toBeDefined();
      expect(rightsCheck).toBeDefined();

      // Verify each check has proper structure
      [httpsCheck, privacyCheck, consentCheck, securityCheck, rightsCheck].forEach(check => {
        expect(check).toHaveProperty('ruleId');
        expect(check).toHaveProperty('ruleName');
        expect(check).toHaveProperty('category');
        expect(check).toHaveProperty('passed');
        expect(check).toHaveProperty('score');
        expect(check).toHaveProperty('weight');
        expect(check).toHaveProperty('severity');
        expect(check).toHaveProperty('evidence');
        expect(check).toHaveProperty('recommendations');
        expect(check).toHaveProperty('manualReviewRequired');
      });
    });
  });
});
