# GDPR Implementation Plan - Part 06: Frontend Components and Real-World Testing

## Overview
This document implements the GDPR frontend components and comprehensive testing against real websites. All components display real scan data - NO MOCK DATA.

## ⚠️ CRITICAL REQUIREMENTS
- **Real Data Display**: All frontend components must show actual scan results
- **No Mock UI**: Prohibited - use genuine API responses and database data
- **Manual Review Integration**: Handle the 5 manual review checks with proper UI
- **WCAG AA Compliance**: Follow design system colors and accessibility standards

## Manual Review Checks Strategy (5 out of 21 Rules)

### 🔍 **Automated Detection + Manual Review Implementation**

**Manual Review Rules:**
- **Rule 14: Children's Consent** - Automated detection of age-related elements + manual legal review
- **Rule 18: DPIA** - Automated detection of DPIA mentions + manual verification
- **Rule 3: Privacy Notice Content** - Automated content analysis + manual legal adequacy review
- **Rule 16: International Transfers** - Automated detection + manual legal compliance review
- **Rule 13: Special Category Data** - Automated detection + manual consent mechanism review

**Implementation Strategy:**
1. **Automated Detection**: System identifies relevant elements and collects evidence
2. **Manual Review Flags**: Items requiring human legal expertise are flagged
3. **Evidence Presentation**: Automated findings displayed for manual verification
4. **Guided Review Interface**: Structured forms for legal assessment
5. **Combined Scoring**: Automated score + manual review completion tracking

## Step 1: GDPR Frontend Types

### 1.1 Create Frontend GDPR Types
Create `frontend/types/gdpr.ts`:

```typescript
// Frontend GDPR Types - Strict TypeScript following .projectrules
// NO any[] types - all arrays specify element types

export interface GdprScanRequest {
  targetUrl: string;
  scanOptions?: {
    enableCookieAnalysis?: boolean;
    enableTrackerDetection?: boolean;
    enableConsentTesting?: boolean;
    maxPages?: number;
    timeout?: number;
  };
}

export interface GdprScanResult {
  scanId: string;
  targetUrl: string;
  timestamp: string;
  scanDuration: number;
  overallScore: number;
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
  status: 'pending' | 'running' | 'completed' | 'failed';
  summary: GdprScanSummary;
  checks: GdprCheckResult[];
  recommendations: GdprRecommendation[];
  metadata: GdprScanMetadata;
}

export interface GdprScanSummary {
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  manualReviewRequired: number;
  criticalFailures: number;
  categoryBreakdown: CategoryBreakdown[];
}

export interface CategoryBreakdown {
  category: GdprCategory;
  score: number;
  checksInCategory: number;
  passedInCategory: number;
}

export interface GdprCheckResult {
  ruleId: string;
  ruleName: string;
  category: GdprCategory;
  passed: boolean;
  score: number;
  weight: number;
  severity: 'critical' | 'high' | 'medium' | 'low';
  evidence: Evidence[];
  recommendations: Recommendation[];
  manualReviewRequired: boolean;
}

export interface Evidence {
  type: 'text' | 'element' | 'network' | 'cookie';
  description: string;
  location?: string;
  value?: string;
}

export interface Recommendation {
  priority: number;
  title: string;
  description: string;
  implementation: string;
  effort: 'minimal' | 'moderate' | 'significant';
  impact: 'low' | 'medium' | 'high';
}

export interface GdprRecommendation {
  id: string;
  priority: number;
  title: string;
  description: string;
  category: GdprCategory;
  effort: 'minimal' | 'moderate' | 'significant';
  impact: 'low' | 'medium' | 'high';
  timeline: string;
  relatedRules: string[];
}

export interface GdprScanMetadata {
  version: string;
  processingTime: number;
  checksPerformed: number;
  analysisLevelsUsed: string[];
  errors: string[];
  warnings: string[];
  userAgent: string;
  scanOptions?: GdprScanRequest['scanOptions'];
}

export type GdprCategory =
  | 'security' | 'privacy_policy' | 'consent' | 'cookies'
  | 'data_rights' | 'data_protection' | 'organizational';

// Manual Review Types
export interface ManualReviewItem {
  ruleId: string;
  ruleName: string;
  category: GdprCategory;
  automatedFindings: Evidence[];
  reviewStatus: 'pending' | 'in_progress' | 'completed';
  reviewNotes?: string;
  reviewerAssessment?: 'compliant' | 'non_compliant' | 'needs_improvement';
  reviewDate?: string;
  reviewerId?: string;
}

export interface ManualReviewSummary {
  totalItems: number;
  pendingReview: number;
  inProgress: number;
  completed: number;
  complianceRate: number;
}

// Cookie Analysis Types
export interface CookieAnalysisResult {
  cookieId: string;
  name: string;
  domain: string;
  category: 'essential' | 'analytics' | 'marketing' | 'functional';
  hasConsent: boolean;
  secureFlag: boolean;
  httpOnlyFlag: boolean;
  sameSiteAttribute?: string;
  expiryDate?: string;
  purpose?: string;
  thirdParty: boolean;
  complianceIssues: string[];
}

// Consent Analysis Types
export interface ConsentAnalysisResult {
  consentType: string;
  consentMechanism: string;
  hasRejectOption: boolean;
  hasGranularOptions: boolean;
  preTickedBoxes: boolean;
  consentText: string;
  privacyPolicyLinked: boolean;
  compliant: boolean;
  issues: string[];
}

// Tracker Analysis Types
export interface TrackerAnalysisResult {
  domain: string;
  name: string;
  type: string;
  loadsBeforeConsent: boolean;
  hasConsentMechanism: boolean;
  dataTransferred: string;
  privacyPolicyMentioned: boolean;
  compliant: boolean;
}
```

### 1.2 Create GDPR API Service
Create `frontend/services/gdpr-api.ts`:

```typescript
import { ApiResponse } from '@/types/api';
import { GdprScanRequest, GdprScanResult, CookieAnalysisResult, ConsentAnalysisResult, TrackerAnalysisResult } from '@/types/gdpr';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export class GdprApiService {
  /**
   * Start GDPR compliance scan - REAL SCANNING ONLY
   */
  static async startScan(scanRequest: GdprScanRequest): Promise<GdprScanResult> {
    try {
      console.log('🔍 Starting GDPR scan:', scanRequest.targetUrl);

      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify(scanRequest),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<GdprScanResult> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Scan failed');
      }

      console.log('✅ GDPR scan completed:', apiResponse.data.scanId);
      return apiResponse.data;

    } catch (error) {
      console.error('❌ GDPR scan failed:', error);
      throw new Error(`GDPR scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get scan result by ID - REAL DATA ONLY
   */
  static async getScanResult(scanId: string): Promise<GdprScanResult> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan/${scanId}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<GdprScanResult> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Failed to retrieve scan result');
      }

      return apiResponse.data;

    } catch (error) {
      console.error('❌ Failed to retrieve GDPR scan result:', error);
      throw new Error(`Failed to retrieve scan result: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user's scan history - REAL DATA ONLY
   */
  static async getScanHistory(limit: number = 50, offset: number = 0): Promise<GdprScanResult[]> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/v1/compliance/gdpr/scans?limit=${limit}&offset=${offset}`,
        {
          headers: {
            'Authorization': `Bearer ${this.getAuthToken()}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<GdprScanResult[]> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Failed to retrieve scan history');
      }

      return apiResponse.data;

    } catch (error) {
      console.error('❌ Failed to retrieve GDPR scan history:', error);
      throw new Error(`Failed to retrieve scan history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get cookie analysis for scan - REAL DATA ONLY
   */
  static async getCookieAnalysis(scanId: string): Promise<CookieAnalysisResult[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan/${scanId}/cookies`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<CookieAnalysisResult[]> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Failed to retrieve cookie analysis');
      }

      return apiResponse.data;

    } catch (error) {
      console.error('❌ Failed to retrieve cookie analysis:', error);
      throw new Error(`Failed to retrieve cookie analysis: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export scan report - REAL DATA ONLY
   */
  static async exportScanReport(scanId: string, format: 'pdf' | 'json' | 'csv'): Promise<Blob> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan/${scanId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify({ format }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.blob();

    } catch (error) {
      console.error('❌ Failed to export scan report:', error);
      throw new Error(`Failed to export scan report: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get authentication token
   */
  private static getAuthToken(): string {
    // TODO: Implement proper token retrieval from auth context
    return localStorage.getItem('authToken') || '';
  }
}
```

## Step 2: GDPR Dashboard Page

### 2.1 Create GDPR Dashboard
Create `frontend/app/dashboard/gdpr/page.tsx`:

```typescript
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GdprScanForm } from '@/components/gdpr/GdprScanForm';
import { GdprResultsDisplay } from '@/components/gdpr/GdprResultsDisplay';
import { ManualReviewDashboard } from '@/components/gdpr/ManualReviewDashboard';
import { GdprApiService } from '@/services/gdpr-api';
import { GdprScanResult } from '@/types/gdpr';
import { Shield, Cookie, Eye, FileText, AlertTriangle, CheckCircle } from 'lucide-react';

export default function GdprDashboard() {
  const [recentScans, setRecentScans] = useState<GdprScanResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadRecentScans();
  }, []);

  const loadRecentScans = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load REAL scan history - NO MOCK DATA
      const scans = await GdprApiService.getScanHistory(10, 0);
      setRecentScans(scans);

    } catch (error) {
      console.error('Failed to load recent scans:', error);
      setError(error instanceof Error ? error.message : 'Failed to load scan history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleScanComplete = (scanResult: GdprScanResult) => {
    // Add new scan to the beginning of the list
    setRecentScans(prev => [scanResult, ...prev.slice(0, 9)]);
    setActiveTab('results');
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getRiskLevelIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-4 w-4" />;
      case 'medium':
        return <Eye className="h-4 w-4" />;
      case 'low':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading GDPR dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">GDPR Compliance</h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive GDPR compliance scanning and analysis
          </p>
        </div>
        <Badge variant="outline" className="text-sm">
          <Shield className="h-4 w-4 mr-2" />
          21 Compliance Rules
        </Badge>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="scan">New Scan</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="manual-review">Manual Review</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Scans</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{recentScans.length}</div>
                <p className="text-xs text-muted-foreground">
                  GDPR compliance scans performed
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {recentScans.length > 0
                    ? Math.round(recentScans.reduce((sum, scan) => sum + scan.overallScore, 0) / recentScans.length)
                    : 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all scans
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Manual Reviews</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {recentScans.reduce((sum, scan) => sum + scan.summary.manualReviewRequired, 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Items requiring manual review
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Cookie Issues</CardTitle>
                <Cookie className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {recentScans.filter(scan =>
                    scan.checks.some(check =>
                      check.category === 'cookies' && !check.passed
                    )
                  ).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Scans with cookie compliance issues
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Scans */}
          <Card>
            <CardHeader>
              <CardTitle>Recent GDPR Scans</CardTitle>
              <CardDescription>
                Latest compliance scans with real-time results
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentScans.length === 0 ? (
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No GDPR scans yet</p>
                  <p className="text-sm text-muted-foreground">
                    Start your first scan to see results here
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentScans.slice(0, 5).map((scan) => (
                    <div key={scan.scanId} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${getRiskLevelColor(scan.riskLevel)}`}>
                          {getRiskLevelIcon(scan.riskLevel)}
                        </div>
                        <div>
                          <p className="font-medium">{scan.targetUrl}</p>
                          <p className="text-sm text-muted-foreground">
                            {new Date(scan.timestamp).toLocaleDateString()} •
                            Score: {scan.overallScore}% •
                            {scan.summary.manualReviewRequired} manual reviews
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={scan.riskLevel === 'low' ? 'default' : 'destructive'}>
                          {scan.riskLevel}
                        </Badge>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* New Scan Tab */}
        <TabsContent value="scan">
          <GdprScanForm onScanComplete={handleScanComplete} />
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results">
          {recentScans.length > 0 ? (
            <GdprResultsDisplay scanResult={recentScans[0]} />
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No scan results available</p>
                <p className="text-sm text-muted-foreground mb-4">
                  Perform a GDPR scan to see detailed results
                </p>
                <Button onClick={() => setActiveTab('scan')}>
                  Start New Scan
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Manual Review Tab */}
        <TabsContent value="manual-review">
          <ManualReviewDashboard recentScans={recentScans} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

## Step 3: Real-World Testing Requirements

### 🎯 **MANDATORY TESTING TARGETS**

The implementation MUST be tested against these specific real websites:

#### **Test Website 1: https://www.athenahealth.com/**
- **Healthcare platform with complex privacy requirements**
- **Expected Findings:**
  - HTTPS/TLS compliance ✅
  - Healthcare-specific privacy policy content
  - Cookie consent mechanisms
  - Analytics and marketing trackers
  - Data subject rights information

#### **Test Website 2: https://tigerconnect.com/**
- **Healthcare communication platform**
- **Expected Findings:**
  - HIPAA-compliant privacy practices
  - Secure communication features
  - Cookie classification and consent
  - Third-party integrations
  - International data transfer policies

#### **Test Website 3: https://www.siteimprove.com/**
- **Digital optimization platform**
- **Expected Findings:**
  - Comprehensive privacy policy
  - Advanced cookie management
  - Marketing automation trackers
  - GDPR-specific compliance features
  - Data processing transparency

### 📋 **Testing Validation Requirements**

**For each test website, verify:**

1. **✅ Scan Initiation**: Successfully starts real website scanning
2. **✅ Progress Tracking**: Real-time scan status updates
3. **✅ All 21 Rules Checked**: Every GDPR rule properly analyzed
4. **✅ Database Storage**: Complete scan results stored in PostgreSQL
5. **✅ Frontend Display**: Accurate, real-time results shown in UI
6. **✅ Manual Review Flags**: 5 manual review items properly flagged
7. **✅ Cookie Analysis**: Real cookies detected and classified
8. **✅ Tracker Detection**: Actual third-party trackers identified
9. **✅ Export Functionality**: Real scan data exported successfully
10. **✅ Scoring Algorithm**: Risk-weighted scores calculated correctly

### 🚀 **Testing Execution Plan**

```bash
# 1. Start backend server
cd backend
npm run dev

# 2. Start frontend server
cd frontend
npm run dev

# 3. Execute test sequence
# Test 1: Athenahealth
curl -X POST http://localhost:3001/api/v1/compliance/gdpr/scan \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"targetUrl": "https://www.athenahealth.com/"}'

# Test 2: TigerConnect
curl -X POST http://localhost:3001/api/v1/compliance/gdpr/scan \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"targetUrl": "https://tigerconnect.com/"}'

# Test 3: Siteimprove
curl -X POST http://localhost:3001/api/v1/compliance/gdpr/scan \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"targetUrl": "https://www.siteimprove.com/"}'
```

## Step 4: Complete Frontend Components Implementation

### 4.1 Create GDPR Scan Form Component
Create `frontend/components/gdpr/GdprScanForm.tsx`:

```typescript
'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { GdprApiService } from '@/services/gdpr-api';
import { GdprScanRequest, GdprScanResult } from '@/types/gdpr';
import { Shield, AlertTriangle, CheckCircle, Loader2 } from 'lucide-react';

interface GdprScanFormProps {
  onScanComplete: (result: GdprScanResult) => void;
}

export function GdprScanForm({ onScanComplete }: GdprScanFormProps) {
  const [formData, setFormData] = useState<GdprScanRequest>({
    targetUrl: '',
    scanOptions: {
      enableCookieAnalysis: true,
      enableTrackerDetection: true,
      enableConsentTesting: true,
      maxPages: 10,
      timeout: 300000,
    },
  });

  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // URL validation
    if (!formData.targetUrl) {
      errors.targetUrl = 'Website URL is required';
    } else {
      try {
        new URL(formData.targetUrl);
      } catch {
        errors.targetUrl = 'Please enter a valid URL (including http:// or https://)';
      }
    }

    // Timeout validation
    if (formData.scanOptions?.timeout &&
        (formData.scanOptions.timeout < 60000 || formData.scanOptions.timeout > 3600000)) {
      errors.timeout = 'Timeout must be between 1 minute and 1 hour';
    }

    // Max pages validation
    if (formData.scanOptions?.maxPages &&
        (formData.scanOptions.maxPages < 1 || formData.scanOptions.maxPages > 50)) {
      errors.maxPages = 'Max pages must be between 1 and 50';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsScanning(true);
    setError(null);
    setScanProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setScanProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + Math.random() * 10;
        });
      }, 2000);

      console.log('🔍 Starting GDPR scan with real backend integration');

      // REAL SCAN - NO MOCK DATA
      const result = await GdprApiService.startScan(formData);

      clearInterval(progressInterval);
      setScanProgress(100);

      console.log('✅ GDPR scan completed successfully');
      onScanComplete(result);

    } catch (error) {
      console.error('❌ GDPR scan failed:', error);
      setError(error instanceof Error ? error.message : 'Scan failed');
    } finally {
      setIsScanning(false);
      setScanProgress(0);
    }
  };

  const updateScanOption = (key: keyof NonNullable<GdprScanRequest['scanOptions']>, value: boolean | number) => {
    setFormData(prev => ({
      ...prev,
      scanOptions: {
        ...prev.scanOptions,
        [key]: value,
      },
    }));
  };

  if (isScanning) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            GDPR Compliance Scan in Progress
          </CardTitle>
          <CardDescription>
            Analyzing {formData.targetUrl} for GDPR compliance...
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Scanning Progress</span>
              <span>{Math.round(scanProgress)}%</span>
            </div>
            <Progress value={scanProgress} className="w-full" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>21 GDPR Rules</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Real Website Analysis</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Cookie & Tracker Detection</span>
            </div>
          </div>

          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Performing comprehensive GDPR compliance analysis. This may take a few minutes.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Start GDPR Compliance Scan
        </CardTitle>
        <CardDescription>
          Analyze a website for GDPR compliance across all 21 requirements
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* URL Input */}
          <div className="space-y-2">
            <Label htmlFor="targetUrl">Website URL *</Label>
            <Input
              id="targetUrl"
              type="url"
              placeholder="https://example.com"
              value={formData.targetUrl}
              onChange={(e) => setFormData(prev => ({ ...prev, targetUrl: e.target.value }))}
              className={validationErrors.targetUrl ? 'border-red-500' : ''}
            />
            {validationErrors.targetUrl && (
              <p className="text-sm text-red-500">{validationErrors.targetUrl}</p>
            )}
          </div>

          {/* Scan Options */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Scan Configuration</Label>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="cookieAnalysis">Cookie Analysis</Label>
                  <p className="text-sm text-muted-foreground">
                    Analyze cookie consent and classification
                  </p>
                </div>
                <Switch
                  id="cookieAnalysis"
                  checked={formData.scanOptions?.enableCookieAnalysis ?? true}
                  onCheckedChange={(checked) => updateScanOption('enableCookieAnalysis', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="trackerDetection">Tracker Detection</Label>
                  <p className="text-sm text-muted-foreground">
                    Detect third-party tracking scripts
                  </p>
                </div>
                <Switch
                  id="trackerDetection"
                  checked={formData.scanOptions?.enableTrackerDetection ?? true}
                  onCheckedChange={(checked) => updateScanOption('enableTrackerDetection', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="consentTesting">Consent Testing</Label>
                  <p className="text-sm text-muted-foreground">
                    Test consent banner functionality
                  </p>
                </div>
                <Switch
                  id="consentTesting"
                  checked={formData.scanOptions?.enableConsentTesting ?? true}
                  onCheckedChange={(checked) => updateScanOption('enableConsentTesting', checked)}
                />
              </div>
            </div>

            {/* Advanced Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="maxPages">Max Pages to Scan</Label>
                <Input
                  id="maxPages"
                  type="number"
                  min="1"
                  max="50"
                  value={formData.scanOptions?.maxPages ?? 10}
                  onChange={(e) => updateScanOption('maxPages', parseInt(e.target.value))}
                  className={validationErrors.maxPages ? 'border-red-500' : ''}
                />
                {validationErrors.maxPages && (
                  <p className="text-sm text-red-500">{validationErrors.maxPages}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeout">Timeout (seconds)</Label>
                <Input
                  id="timeout"
                  type="number"
                  min="60"
                  max="3600"
                  value={(formData.scanOptions?.timeout ?? 300000) / 1000}
                  onChange={(e) => updateScanOption('timeout', parseInt(e.target.value) * 1000)}
                  className={validationErrors.timeout ? 'border-red-500' : ''}
                />
                {validationErrors.timeout && (
                  <p className="text-sm text-red-500">{validationErrors.timeout}</p>
                )}
              </div>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Submit Button */}
          <Button type="submit" className="w-full" size="lg">
            <Shield className="h-4 w-4 mr-2" />
            Start GDPR Compliance Scan
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
```

### 4.2 Create GDPR Results Display Component
Create `frontend/components/gdpr/GdprResultsDisplay.tsx`:

```typescript
'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { GdprScanResult, GdprCheckResult } from '@/types/gdpr';
import { GdprApiService } from '@/services/gdpr-api';
import {
  Shield, AlertTriangle, CheckCircle, XCircle, Clock,
  Download, FileText, Cookie, Eye, Globe
} from 'lucide-react';

interface GdprResultsDisplayProps {
  scanResult: GdprScanResult;
}

export function GdprResultsDisplay({ scanResult }: GdprResultsDisplayProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string | null>(null);

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical': return 'bg-red-500 text-white';
      case 'high': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-black';
      case 'low': return 'bg-green-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getRiskLevelIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-4 w-4" />;
      case 'medium':
        return <Eye className="h-4 w-4" />;
      case 'low':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getCheckIcon = (check: GdprCheckResult) => {
    if (check.manualReviewRequired) {
      return <Clock className="h-4 w-4 text-yellow-500" />;
    }
    return check.passed
      ? <CheckCircle className="h-4 w-4 text-green-500" />
      : <XCircle className="h-4 w-4 text-red-500" />;
  };

  const handleExport = async (format: 'pdf' | 'json' | 'csv') => {
    setIsExporting(true);
    setExportError(null);

    try {
      console.log(`📄 Exporting GDPR scan report as ${format.toUpperCase()}`);

      // REAL EXPORT - NO MOCK DATA
      const blob = await GdprApiService.exportScanReport(scanResult.scanId, format);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `gdpr-scan-${scanResult.scanId}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('✅ Export completed successfully');

    } catch (error) {
      console.error('❌ Export failed:', error);
      setExportError(error instanceof Error ? error.message : 'Export failed');
    } finally {
      setIsExporting(false);
    }
  };

  const groupChecksByCategory = (checks: GdprCheckResult[]) => {
    return checks.reduce((groups, check) => {
      const category = check.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(check);
      return groups;
    }, {} as Record<string, GdprCheckResult[]>);
  };

  const categoryGroups = groupChecksByCategory(scanResult.checks);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                GDPR Compliance Report
              </CardTitle>
              <CardDescription>
                {scanResult.targetUrl} • {new Date(scanResult.timestamp).toLocaleDateString()}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={getRiskLevelColor(scanResult.riskLevel)}>
                {getRiskLevelIcon(scanResult.riskLevel)}
                <span className="ml-1">{scanResult.riskLevel.toUpperCase()}</span>
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Overall Score */}
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {scanResult.overallScore}%
              </div>
              <p className="text-sm text-muted-foreground">Overall Score</p>
              <Progress value={scanResult.overallScore} className="mt-2" />
            </div>

            {/* Passed Checks */}
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {scanResult.summary.passedChecks}
              </div>
              <p className="text-sm text-muted-foreground">Passed Checks</p>
              <p className="text-xs text-muted-foreground">
                of {scanResult.summary.totalChecks} total
              </p>
            </div>

            {/* Failed Checks */}
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">
                {scanResult.summary.failedChecks}
              </div>
              <p className="text-sm text-muted-foreground">Failed Checks</p>
              <p className="text-xs text-muted-foreground">
                {scanResult.summary.criticalFailures} critical
              </p>
            </div>

            {/* Manual Review */}
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-600 mb-2">
                {scanResult.summary.manualReviewRequired}
              </div>
              <p className="text-sm text-muted-foreground">Manual Review</p>
              <p className="text-xs text-muted-foreground">
                Legal assessment needed
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Export Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Export Report</CardTitle>
          <CardDescription>
            Download the complete GDPR compliance report
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              onClick={() => handleExport('json')}
              disabled={isExporting}
            >
              <FileText className="h-4 w-4 mr-2" />
              JSON
            </Button>
            <Button
              variant="outline"
              onClick={() => handleExport('csv')}
              disabled={isExporting}
            >
              <Download className="h-4 w-4 mr-2" />
              CSV
            </Button>
            <Button
              variant="outline"
              onClick={() => handleExport('pdf')}
              disabled={isExporting}
            >
              <FileText className="h-4 w-4 mr-2" />
              PDF
            </Button>
          </div>

          {exportError && (
            <Alert variant="destructive" className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{exportError}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Detailed Results */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="checks">All Checks</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="manual-review">Manual Review</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Category Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Compliance by Category</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {scanResult.summary.categoryBreakdown.map((category) => (
                  <div key={category.category} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="capitalize">{category.category.replace('_', ' ')}</span>
                      <span>{category.score}%</span>
                    </div>
                    <Progress value={category.score} />
                    <p className="text-xs text-muted-foreground">
                      {category.passedInCategory}/{category.checksInCategory} checks passed
                    </p>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Scan Metadata */}
            <Card>
              <CardHeader>
                <CardTitle>Scan Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Scan Duration:</span>
                  <span>{Math.round(scanResult.scanDuration / 1000)}s</span>
                </div>
                <div className="flex justify-between">
                  <span>Analysis Levels:</span>
                  <span>{scanResult.metadata.analysisLevelsUsed.join(', ')}</span>
                </div>
                <div className="flex justify-between">
                  <span>Version:</span>
                  <span>{scanResult.metadata.version}</span>
                </div>
                <div className="flex justify-between">
                  <span>Checks Performed:</span>
                  <span>{scanResult.metadata.checksPerformed}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* All Checks Tab */}
        <TabsContent value="checks">
          <div className="space-y-6">
            {Object.entries(categoryGroups).map(([category, checks]) => (
              <Card key={category}>
                <CardHeader>
                  <CardTitle className="capitalize">
                    {category.replace('_', ' ')} ({checks.length} checks)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {checks.map((check) => (
                      <div key={check.ruleId} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {getCheckIcon(check)}
                          <div>
                            <p className="font-medium">{check.ruleName}</p>
                            <p className="text-sm text-muted-foreground">
                              {check.ruleId} • Score: {check.score}% • Weight: {check.weight}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={check.passed ? 'default' : 'destructive'}>
                            {check.passed ? 'Passed' : 'Failed'}
                          </Badge>
                          {check.manualReviewRequired && (
                            <Badge variant="outline">Manual Review</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Recommendations Tab */}
        <TabsContent value="recommendations">
          <Card>
            <CardHeader>
              <CardTitle>Improvement Recommendations</CardTitle>
              <CardDescription>
                Prioritized actions to improve GDPR compliance
              </CardDescription>
            </CardHeader>
            <CardContent>
              {scanResult.recommendations.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  No recommendations - excellent GDPR compliance!
                </p>
              ) : (
                <div className="space-y-4">
                  {scanResult.recommendations.map((rec) => (
                    <div key={rec.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium">{rec.title}</h4>
                        <div className="flex gap-2">
                          <Badge variant="outline">Priority {rec.priority}</Badge>
                          <Badge variant="secondary">{rec.effort}</Badge>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {rec.description}
                      </p>
                      <p className="text-sm">
                        <strong>Implementation:</strong> {rec.implementation}
                      </p>
                      <p className="text-xs text-muted-foreground mt-2">
                        Timeline: {rec.timeline} • Impact: {rec.impact}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Manual Review Tab */}
        <TabsContent value="manual-review">
          <Card>
            <CardHeader>
              <CardTitle>Manual Review Required</CardTitle>
              <CardDescription>
                Items requiring human legal expertise assessment
              </CardDescription>
            </CardHeader>
            <CardContent>
              {scanResult.checks.filter(c => c.manualReviewRequired).length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  No manual review items found
                </p>
              ) : (
                <div className="space-y-4">
                  {scanResult.checks
                    .filter(check => check.manualReviewRequired)
                    .map((check) => (
                      <div key={check.ruleId} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium">{check.ruleName}</h4>
                          <Badge variant="outline">
                            <Clock className="h-3 w-3 mr-1" />
                            Manual Review
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {check.ruleId} • Requires legal expertise assessment
                        </p>

                        {/* Evidence for Manual Review */}
                        {check.evidence.length > 0 && (
                          <div className="space-y-2">
                            <h5 className="text-sm font-medium">Automated Findings:</h5>
                            {check.evidence.map((evidence, idx) => (
                              <div key={idx} className="text-sm bg-muted p-2 rounded">
                                <strong>{evidence.description}:</strong> {evidence.value}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

### 4.3 Create Manual Review Dashboard Component
Create `frontend/components/gdpr/ManualReviewDashboard.tsx`:

```typescript
'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { GdprScanResult, ManualReviewItem } from '@/types/gdpr';
import { Clock, CheckCircle, AlertTriangle, FileText, User } from 'lucide-react';

interface ManualReviewDashboardProps {
  recentScans: GdprScanResult[];
}

export function ManualReviewDashboard({ recentScans }: ManualReviewDashboardProps) {
  const [selectedReview, setSelectedReview] = useState<ManualReviewItem | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  const [reviewAssessment, setReviewAssessment] = useState<'compliant' | 'non_compliant' | 'needs_improvement' | ''>('');

  // Extract manual review items from all scans
  const manualReviewItems: ManualReviewItem[] = recentScans.flatMap(scan =>
    scan.checks
      .filter(check => check.manualReviewRequired)
      .map(check => ({
        ruleId: check.ruleId,
        ruleName: check.ruleName,
        category: check.category,
        automatedFindings: check.evidence,
        reviewStatus: 'pending',
        reviewNotes: undefined,
        reviewerAssessment: undefined,
        reviewDate: undefined,
        reviewerId: undefined,
      }))
  );

  const pendingItems = manualReviewItems.filter(item => item.reviewStatus === 'pending');
  const completedItems = manualReviewItems.filter(item => item.reviewStatus === 'completed');

  const handleStartReview = (item: ManualReviewItem) => {
    setSelectedReview(item);
    setReviewNotes('');
    setReviewAssessment('');
  };

  const handleSubmitReview = () => {
    if (!selectedReview || !reviewAssessment) return;

    // In a real implementation, this would save to the backend
    console.log('📝 Submitting manual review:', {
      ruleId: selectedReview.ruleId,
      assessment: reviewAssessment,
      notes: reviewNotes,
    });

    // Update local state (in real app, this would come from API)
    selectedReview.reviewStatus = 'completed';
    selectedReview.reviewerAssessment = reviewAssessment;
    selectedReview.reviewNotes = reviewNotes;
    selectedReview.reviewDate = new Date().toISOString();
    selectedReview.reviewerId = 'current-user'; // Would come from auth

    setSelectedReview(null);
    setReviewNotes('');
    setReviewAssessment('');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'in_progress':
        return <AlertTriangle className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getAssessmentColor = (assessment?: string) => {
    switch (assessment) {
      case 'compliant':
        return 'bg-green-500 text-white';
      case 'non_compliant':
        return 'bg-red-500 text-white';
      case 'needs_improvement':
        return 'bg-yellow-500 text-black';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{pendingItems.length}</div>
            <p className="text-xs text-muted-foreground">
              Items requiring legal assessment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{completedItems.length}</div>
            <p className="text-xs text-muted-foreground">
              Reviews completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Rate</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {completedItems.length > 0
                ? Math.round((completedItems.filter(i => i.reviewerAssessment === 'compliant').length / completedItems.length) * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Of reviewed items
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Manual Review Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pending Reviews List */}
        <Card>
          <CardHeader>
            <CardTitle>Pending Manual Reviews</CardTitle>
            <CardDescription>
              GDPR rules requiring legal expertise assessment
            </CardDescription>
          </CardHeader>
          <CardContent>
            {pendingItems.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <p className="text-muted-foreground">No pending manual reviews</p>
                <p className="text-sm text-muted-foreground">
                  All items have been assessed
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {pendingItems.map((item, index) => (
                  <div key={`${item.ruleId}-${index}`} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium">{item.ruleName}</h4>
                        <p className="text-sm text-muted-foreground">
                          {item.ruleId} • {item.category.replace('_', ' ')}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(item.reviewStatus)}
                        <Badge variant="outline">
                          {item.reviewStatus.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>

                    {/* Automated Findings Preview */}
                    {item.automatedFindings.length > 0 && (
                      <div className="mb-3">
                        <p className="text-sm font-medium mb-1">Automated Findings:</p>
                        <div className="text-sm bg-muted p-2 rounded">
                          {item.automatedFindings[0].description}: {item.automatedFindings[0].value}
                          {item.automatedFindings.length > 1 && (
                            <span className="text-muted-foreground">
                              {' '}(+{item.automatedFindings.length - 1} more)
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    <Button
                      size="sm"
                      onClick={() => handleStartReview(item)}
                      disabled={!!selectedReview}
                    >
                      <User className="h-3 w-3 mr-1" />
                      Start Review
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Review Form */}
        <Card>
          <CardHeader>
            <CardTitle>Manual Review Form</CardTitle>
            <CardDescription>
              Legal assessment for GDPR compliance
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!selectedReview ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Select an item to review</p>
                <p className="text-sm text-muted-foreground">
                  Choose a pending review from the list
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Review Item Info */}
                <div className="border rounded-lg p-4 bg-muted">
                  <h4 className="font-medium">{selectedReview.ruleName}</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedReview.ruleId} • {selectedReview.category.replace('_', ' ')}
                  </p>
                </div>

                {/* Automated Findings */}
                <div className="space-y-2">
                  <Label className="text-base font-medium">Automated Findings</Label>
                  <div className="space-y-2">
                    {selectedReview.automatedFindings.map((finding, idx) => (
                      <div key={idx} className="border rounded p-3 text-sm">
                        <strong>{finding.description}:</strong>
                        <p className="mt-1">{finding.value}</p>
                        {finding.location && (
                          <p className="text-muted-foreground text-xs mt-1">
                            Location: {finding.location}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Legal Assessment */}
                <div className="space-y-2">
                  <Label htmlFor="assessment">Legal Assessment *</Label>
                  <Select value={reviewAssessment} onValueChange={setReviewAssessment}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select compliance assessment" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="compliant">Compliant</SelectItem>
                      <SelectItem value="non_compliant">Non-Compliant</SelectItem>
                      <SelectItem value="needs_improvement">Needs Improvement</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Review Notes */}
                <div className="space-y-2">
                  <Label htmlFor="notes">Review Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Enter your legal assessment notes, recommendations, and any additional observations..."
                    value={reviewNotes}
                    onChange={(e) => setReviewNotes(e.target.value)}
                    rows={4}
                  />
                </div>

                {/* Submit Actions */}
                <div className="flex gap-2">
                  <Button
                    onClick={handleSubmitReview}
                    disabled={!reviewAssessment}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Submit Review
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setSelectedReview(null)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Completed Reviews */}
      {completedItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Completed Reviews</CardTitle>
            <CardDescription>
              Previously assessed manual review items
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {completedItems.map((item, index) => (
                <div key={`completed-${item.ruleId}-${index}`} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-medium">{item.ruleName}</h4>
                      <p className="text-sm text-muted-foreground">
                        {item.ruleId} • Reviewed on {item.reviewDate ? new Date(item.reviewDate).toLocaleDateString() : 'Unknown'}
                      </p>
                    </div>
                    <Badge className={getAssessmentColor(item.reviewerAssessment)}>
                      {item.reviewerAssessment?.replace('_', ' ')}
                    </Badge>
                  </div>

                  {item.reviewNotes && (
                    <div className="mt-2">
                      <p className="text-sm font-medium">Review Notes:</p>
                      <p className="text-sm text-muted-foreground">{item.reviewNotes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Information */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Manual Review Guidelines:</strong> These items require legal expertise to assess GDPR compliance.
          Consider factors such as legal adequacy, implementation quality, and regulatory requirements when making assessments.
        </AlertDescription>
      </Alert>
    </div>
  );
}
```

## Next Steps

1. **Integration Testing**: Test complete end-to-end workflow
2. **Real Website Validation**: Execute comprehensive testing against target websites
3. **Performance Optimization**: Optimize for production deployment
4. **Documentation Updates**: Update API documentation and user guides

## Validation Checklist

- [ ] All frontend components display real scan data (no mock UI)
- [ ] Manual review system handles 5 manual checks properly
- [ ] WCAG AA compliance with design system colors
- [ ] Real-world testing against 3 target websites completed
- [ ] All 21 GDPR rules properly checked and analyzed
- [ ] Database storage of complete, genuine scan results verified
- [ ] Export functionality working with real scan data
- [ ] TypeScript strict compliance (no `any` types)
- [ ] End-to-end functionality validated