/**
 * GDPR Compliance Module - Main Entry Point
 * 
 * This module provides comprehensive GDPR compliance scanning capabilities
 * including cookie analysis, consent mechanism testing, tracker detection,
 * and privacy policy analysis.
 * 
 * @module GDPR
 */

// Export all types
export * from './types';

// Export core orchestrator
export { GdprOrchestrator } from './orchestrator';

// Export constants
export * from './constants';

// Export database layer
export { GdprDatabase } from './database/gdpr-database';

// Export services (will be added in later parts)
// export { CookieAnalysisService } from './services/cookie-analysis-service';
// export { ConsentTestingService } from './services/consent-testing-service';
// export { TrackerDetectionService } from './services/tracker-detection-service';
// export { PrivacyPolicyService } from './services/privacy-policy-service';

// Export checks (will be added in later parts)
// export * from './checks';

// Export utilities
// export * from './utils';

/**
 * GDPR Module Version
 */
export const GDPR_MODULE_VERSION = '1.0.0';

/**
 * Supported GDPR Rules
 */
export const SUPPORTED_GDPR_RULES = [
  'GDPR-001', 'GDPR-002', 'GDPR-003', 'GDPR-004', 'GDPR-005',
  'GDPR-006', 'GDPR-007', 'GDPR-008', 'GDPR-009', 'GDPR-010',
  'GDPR-011', 'GDPR-012', 'GDPR-013', 'GDPR-014', 'GDPR-015',
  'GDPR-016', 'GDPR-017', 'GDPR-018', 'GDPR-019', 'GDPR-020',
  'GDPR-021'
] as const;
