/**
 * GDPR Compliance Orchestrator
 * 
 * This class coordinates all GDPR compliance checks and manages the scanning process.
 * It follows the same pattern as the HIPAA orchestrator for consistency.
 */

import {
  GdprScanRequest,
  GdprScanResult,
  GdprCheckResult,
  GdprScanSummary,
  GdprRecommendation,
  RiskLevel,
  ScanStatus,
  GdprCategory,
  CategoryBreakdown,
  GdprRuleId,
} from './types';
import { GDPR_RULES, GDPR_SCORING_WEIGHTS } from './constants';
import { GdprDatabase } from './database/gdpr-database';
import { v4 as uuidv4 } from 'uuid';

export class GdprOrchestrator {
  private database: GdprDatabase;

  constructor() {
    this.database = new GdprDatabase();
  }

  /**
   * Perform comprehensive GDPR compliance scan
   * REAL WEBSITE SCANNING ONLY - NO MOCK DATA
   */
  async performComprehensiveScan(
    userId: string,
    scanRequest: GdprScanRequest,
  ): Promise<GdprScanResult> {
    const scanId = uuidv4();
    const startTime = Date.now();

    console.log(`🔍 Starting GDPR compliance scan for: ${scanRequest.targetUrl}`);
    console.log(`📋 Scan ID: ${scanId}`);

    try {
      // Update status to running
      await GdprDatabase.updateScanStatus(scanId, 'running');

      // REAL SCANNING IMPLEMENTATION - Execute all 21 GDPR checks
      const checks = await this.executeAllGdprChecks(scanRequest);

      // Calculate overall score using risk-weighted algorithm
      const scoringResult = this.calculateGdprScore(checks);

      const scanDuration = Date.now() - startTime;

      // REAL SCAN RESULT - populated with actual check results
      const result: GdprScanResult = {
        scanId,
        targetUrl: scanRequest.targetUrl,
        timestamp: new Date().toISOString(),
        scanDuration,
        overallScore: scoringResult.overallScore,
        riskLevel: scoringResult.riskLevel,
        status: 'completed',
        summary: {
          totalChecks: checks.length,
          passedChecks: scoringResult.summary.passedChecks,
          failedChecks: scoringResult.summary.failedChecks,
          manualReviewRequired: scoringResult.summary.manualReviewRequired,
          criticalFailures: scoringResult.criticalFailures,
          categoryBreakdown: scoringResult.breakdown,
        },
        checks,
        recommendations: this.generateRecommendations(checks),
        metadata: {
          version: '1.0.0',
          processingTime: scanDuration,
          checksPerformed: checks.length,
          analysisLevelsUsed: ['pattern', 'behavioral', 'content', 'network'],
          errors: checks.filter((c) => !c.passed).map((c) => `${c.ruleName}: Failed`),
          warnings: checks
            .filter((c) => c.manualReviewRequired)
            .map((c) => `${c.ruleName}: Manual review required`),
          userAgent: scanRequest.scanOptions?.userAgent || 'GDPR-Compliance-Scanner/1.0',
          scanOptions: scanRequest.scanOptions,
        },
      };

      // Save results to database
      await GdprDatabase.saveScanResult(userId, result);
      await GdprDatabase.updateScanStatus(scanId, 'completed');

      console.log(`✅ GDPR scan completed successfully: ${scanId}`);
      return result;
    } catch (error) {
      console.error(`❌ GDPR scan failed: ${scanId}`, error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await GdprDatabase.updateScanStatus(scanId, 'failed', errorMessage);

      throw new Error(`GDPR scan failed: ${errorMessage}`);
    }
  }

  /**
   * Execute all 21 GDPR compliance checks
   * REAL IMPLEMENTATION - calls actual check classes
   */
  private async executeAllGdprChecks(scanRequest: GdprScanRequest): Promise<GdprCheckResult[]> {
    const checks: GdprCheckResult[] = [];
    const config = {
      targetUrl: scanRequest.targetUrl,
      timeout: scanRequest.scanOptions?.timeout || 300000,
    };

    try {
      // Import all check classes
      const {
        HttpsTlsCheck,
        PrivacyPolicyCheck,
        PrivacyContentCheck,
        CookieConsentCheck,
        CookieClassificationCheck,
        TrackerDetectionCheck,
        CookieAttributesCheck,
        GpcDntCheck,
        FormConsentCheck,
        SecurityHeadersCheck,
        IpAnonymizationCheck,
        DataRightsCheck,
        SpecialDataCheck,
        ChildrenConsentCheck,
        DpoContactCheck,
        EuRepresentativeCheck,
        DataTransfersCheck,
        BreachNotificationCheck,
        DpiaCheck,
        DataRetentionCheck,
      } = await import('./checks');

      // Execute all checks in parallel for performance
      const checkPromises = [
        new HttpsTlsCheck().performCheck(config),
        new PrivacyPolicyCheck().performCheck(config),
        new PrivacyContentCheck().performCheck(config),
        new CookieConsentCheck().performCheck(config),
        new CookieClassificationCheck().performCheck({ ...config, scanId: 'temp' }),
        new TrackerDetectionCheck().performCheck(config),
        new CookieAttributesCheck().performCheck(config),
        new GpcDntCheck().performCheck(config),
        new FormConsentCheck().performCheck(config),
        new SecurityHeadersCheck().performCheck(config),
        new IpAnonymizationCheck().performCheck(config),
        new DataRightsCheck().performCheck(config),
        new SpecialDataCheck().performCheck(config),
        new ChildrenConsentCheck().performCheck(config),
        new DpoContactCheck().performCheck(config),
        new EuRepresentativeCheck().performCheck(config),
        new DataTransfersCheck().performCheck(config),
        new BreachNotificationCheck().performCheck(config),
        new DpiaCheck().performCheck(config),
        new DataRetentionCheck().performCheck(config),
      ];

      // Wait for all checks to complete
      const results = await Promise.allSettled(checkPromises);

      // Process results and handle any failures
      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        if (result.status === 'fulfilled') {
          checks.push(result.value);
        } else {
          // Create error result for failed check
          const ruleIds = Object.keys(GDPR_RULES);
          const ruleId = ruleIds[i] || `GDPR-${String(i + 1).padStart(3, '0')}`;
          const rule = GDPR_RULES[ruleId as keyof typeof GDPR_RULES];

          checks.push({
            ruleId: ruleId as GdprRuleId,
            ruleName: rule?.name || `Check ${i + 1}`,
            category: rule?.category || 'security',
            passed: false,
            score: 0,
            weight: rule?.weight || 1,
            severity: rule?.severity || 'medium',
            evidence: [
              {
                type: 'text',
                description: 'Check execution failed',
                value: result.reason?.message || 'Unknown error',
              },
            ],
            recommendations: [
              {
                priority: 1,
                title: 'Fix check execution',
                description: 'Resolve check implementation issues',
                implementation: 'Debug and fix check logic',
                effort: 'moderate',
                impact: 'medium',
              },
            ],
            manualReviewRequired: false,
          });
        }
      }

      console.log(`✅ Completed ${checks.length} GDPR checks`);
      return checks;
    } catch (error) {
      console.error('❌ Failed to execute GDPR checks:', error);
      throw new Error(
        `Check execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Calculate GDPR compliance score using risk-weighted algorithm
   */
  private calculateGdprScore(checks: GdprCheckResult[]): {
    overallScore: number;
    riskLevel: RiskLevel;
    criticalFailures: number;
    breakdown: CategoryBreakdown[];
    summary: {
      passedChecks: number;
      failedChecks: number;
      manualReviewRequired: number;
    };
  } {
    const weights = GDPR_SCORING_WEIGHTS;
    let totalWeightedScore = 0;
    let totalWeight = 0;
    let criticalFailures = 0;
    let passedChecks = 0;
    let manualReviewRequired = 0;

    for (const check of checks) {
      const weight = weights[check.ruleId] || 1;
      const score = check.passed ? 100 : 0;

      totalWeightedScore += score * weight;
      totalWeight += weight;

      if (check.passed) passedChecks++;
      if (check.manualReviewRequired) manualReviewRequired++;
      if (!check.passed && weight >= 7) criticalFailures++;
    }

    const baseScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
    const criticalPenalty = criticalFailures * 15;
    const finalScore = Math.max(0, baseScore - criticalPenalty);

    return {
      overallScore: Math.round(finalScore),
      riskLevel: this.determineRiskLevel(finalScore, criticalFailures),
      criticalFailures,
      breakdown: this.generateCategoryBreakdown(checks),
      summary: {
        passedChecks,
        failedChecks: checks.length - passedChecks,
        manualReviewRequired,
      },
    };
  }

  /**
   * Determine risk level based on score and critical failures
   */
  private determineRiskLevel(score: number, criticalFailures: number): RiskLevel {
    if (criticalFailures >= 3 || score < 40) return 'critical';
    if (criticalFailures >= 2 || score < 60) return 'high';
    if (criticalFailures >= 1 || score < 75) return 'medium';
    return 'low';
  }

  /**
   * Generate category breakdown for scoring
   */
  private generateCategoryBreakdown(checks: GdprCheckResult[]): CategoryBreakdown[] {
    const categoryMap = new Map<
      GdprCategory,
      {
        totalChecks: number;
        passedChecks: number;
      }
    >();

    for (const check of checks) {
      const existing = categoryMap.get(check.category) || {
        totalChecks: 0,
        passedChecks: 0,
      };

      existing.totalChecks++;
      if (check.passed) existing.passedChecks++;

      categoryMap.set(check.category, existing);
    }

    return Array.from(categoryMap.entries()).map(([category, data]) => ({
      category,
      score: data.totalChecks > 0 ? Math.round((data.passedChecks / data.totalChecks) * 100) : 0,
      checksInCategory: data.totalChecks,
      passedInCategory: data.passedChecks,
    }));
  }

  /**
   * Generate recommendations based on check results
   */
  private generateRecommendations(checks: GdprCheckResult[]): GdprRecommendation[] {
    const recommendations: GdprRecommendation[] = [];
    let recommendationId = 1;

    for (const check of checks) {
      if (!check.passed && check.recommendations.length > 0) {
        for (const rec of check.recommendations) {
          recommendations.push({
            id: `REC-${recommendationId++}`,
            priority: rec.priority,
            title: rec.title,
            description: rec.description,
            category: check.category,
            effort: rec.effort,
            impact: rec.impact,
            timeline: this.getTimelineFromEffort(rec.effort),
            relatedRules: [check.ruleId],
          });
        }
      }
    }

    // Sort by priority
    return recommendations.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Get timeline estimate based on effort level
   */
  private getTimelineFromEffort(effort: string): string {
    switch (effort) {
      case 'minimal':
        return '1-2 days';
      case 'moderate':
        return '1-2 weeks';
      case 'significant':
        return '2-4 weeks';
      default:
        return '1-2 weeks';
    }
  }

  /**
   * Get scan status
   */
  async getScanStatus(scanId: string): Promise<ScanStatus | null> {
    try {
      const result = await GdprDatabase.getScanResult(scanId);
      return result?.status || null;
    } catch (error) {
      console.error('❌ Failed to get scan status:', error);
      return null;
    }
  }
}
