# GDPR Compliance Implementation Blueprint

## Executive Summary

This document provides a comprehensive technical blueprint for implementing GDPR compliance functionality in the Comply Checker platform. Following the established HIPAA module patterns, this implementation will create a robust, scalable GDPR compliance engine with automated checking capabilities, comprehensive scoring, and detailed reporting.

## ⚠️ CRITICAL IMPLEMENTATION REQUIREMENTS

### 🚫 NO MOCK DATA POLICY
**STRICTLY PROHIBITED**: Once backend and frontend are connected, ALL testing and functionality MUST use real website scanning. No mock data, fake responses, or simulated results are permitted.

### ✅ REAL BACKEND INTEGRATION REQUIREMENTS
- **Real Website Scanning**: All GDPR checks must scan actual websites using live HTTP requests
- **Actual Cookie Analysis**: Monitor real cookies from target websites, not simulated data
- **Live Consent Testing**: Interact with real consent banners and forms
- **Genuine Tracker Detection**: Detect actual third-party trackers and scripts
- **Real Privacy Policy Analysis**: Parse and analyze actual privacy policy content
- **Database Storage**: Store all real scan results in PostgreSQL database
- **End-to-End Functionality**: Complete workflow from scan initiation to result display

### 🎯 MANDATORY TESTING TARGETS
The implementation MUST be tested against these specific real websites:
1. **https://www.athenahealth.com/** - Healthcare platform with complex privacy requirements
2. **https://tigerconnect.com/** - Healthcare communication platform
3. **https://www.siteimprove.com/** - Digital optimization platform

### 📊 REAL-WORLD VALIDATION REQUIREMENTS
- Verify scan initiation and progress tracking with actual websites
- Confirm all 21 GDPR rules are properly checked against real content
- Validate database storage of complete, genuine scan results
- Ensure frontend displays accurate, real-time results from actual scans
- Test export functionality with real scan data
- Validate scoring algorithm with actual compliance findings

## 1. Rule-by-Rule Implementation Strategy

### 1.1 Automated Checks (16 rules)

#### **Rule 1: HTTPS/TLS Encryption**
- **Implementation**: SSL/TLS analyzer service (reuse HIPAA SSLAnalyzer)
- **Technical Approach**: Certificate validation, TLS version check, HSTS header detection
- **Dependencies**: Node.js `tls` module, certificate parsing libraries

#### **Rule 2: Privacy Policy Presence**
- **Implementation**: URL resolver and link detection (adapt HIPAA URLResolver)
- **Technical Approach**: DOM parsing for privacy policy links, link validation
- **Dependencies**: Cheerio, Puppeteer for dynamic content

#### **Rule 3: Privacy Notice Content**
- **Implementation**: 3-level analysis system (pattern matching + NLP + AI)
- **Technical Approach**: GDPR-specific patterns, NLP entity extraction, AI compliance analysis
- **Dependencies**: compromise.js, DistilBERT/LegalBERT

#### **Rule 4: Cookie Consent Banner**
- **Implementation**: DOM analysis and behavioral testing
- **Technical Approach**: Banner detection, button analysis, pre-consent cookie monitoring
- **Dependencies**: Puppeteer for interaction testing

#### **Rule 5: Cookie Classification & Blocking**
- **Implementation**: Cookie monitoring and categorization service
- **Technical Approach**: Cookie interception, category classification, consent state tracking
- **Dependencies**: Puppeteer cookie API, cookie classification database

#### **Rule 6: Third-Party Tracker Detection**
- **Implementation**: Network request monitoring and domain analysis
- **Technical Approach**: Known tracker domain database, script analysis, network call interception
- **Dependencies**: Tracker domain lists, network monitoring tools

#### **Rule 7: Cookie Attributes**
- **Implementation**: Cookie security analysis service
- **Technical Approach**: Cookie attribute parsing, security flag validation
- **Dependencies**: Cookie parsing utilities

#### **Rule 8: Global Privacy Control/DNT**
- **Implementation**: GPC header testing and response validation
- **Technical Approach**: Header injection, behavior monitoring, cookie blocking verification
- **Dependencies**: HTTP client with custom headers

#### **Rule 9: Form Consent Controls**
- **Implementation**: Form analysis and consent detection
- **Technical Approach**: Form field analysis, checkbox detection, consent text validation
- **Dependencies**: DOM parsing, form interaction testing

#### **Rule 10: Security Headers**
- **Implementation**: HTTP header analysis (reuse HIPAA security headers)
- **Technical Approach**: Header presence validation, policy parsing
- **Dependencies**: HTTP response analysis

#### **Rule 11: IP Anonymization**
- **Implementation**: JavaScript code analysis for analytics
- **Technical Approach**: Script parsing, analytics configuration detection
- **Dependencies**: JavaScript AST parsing, analytics pattern database

#### **Rule 12: Data Subject Rights**
- **Implementation**: Link detection and contact form analysis
- **Technical Approach**: Rights-related link detection, contact mechanism validation
- **Dependencies**: Link analysis, form detection

#### **Rule 13: Special Category Data**
- **Implementation**: Form analysis and consent mechanism detection
- **Technical Approach**: Sensitive data field detection, explicit consent validation
- **Dependencies**: Field type analysis, consent pattern matching

#### **Rule 15: DPO/EU Representative**
- **Implementation**: Contact information extraction
- **Technical Approach**: Pattern matching for DPO mentions, contact detail extraction
- **Dependencies**: NLP entity extraction, contact pattern database

#### **Rule 16: International Transfers**
- **Implementation**: Policy text analysis for transfer mentions
- **Technical Approach**: Transfer-related keyword detection, safeguard mechanism identification
- **Dependencies**: Legal text analysis patterns

#### **Rule 17: Breach Notification**
- **Implementation**: Policy text analysis for breach commitments
- **Technical Approach**: Breach notification pattern matching
- **Dependencies**: Legal text pattern database

#### **Rule 19: Data Retention**
- **Implementation**: Retention period extraction from policies
- **Technical Approach**: Time period pattern matching, retention criteria detection
- **Dependencies**: Time expression parsing, retention pattern database

#### **Rule 20: Processor Agreements**
- **Implementation**: Policy analysis for processor mentions
- **Technical Approach**: Processor/sub-processor pattern matching, DPA reference detection
- **Dependencies**: Legal text analysis

#### **Rule 21: Imprint & Contact Details**
- **Implementation**: Contact information extraction and validation
- **Technical Approach**: Organization detail extraction, completeness validation
- **Dependencies**: Contact pattern matching, validation rules

### 1.2 Manual Review Required (5 rules)

#### **Rule 14: Children's Consent**
- **Reason**: Age verification mechanisms vary significantly
- **Support**: Provide detection of age-related elements for manual review

#### **Rule 18: DPIA**
- **Reason**: Requires legal assessment of risk levels
- **Support**: Detect DPIA mentions for manual verification

#### **Rules 3, 16**: Legal Adequacy Review
- **Reason**: Legal compliance requires human expertise
- **Support**: Automated detection with manual verification flags

## ✅ GDPR RULES COVERAGE VERIFICATION

### Complete Coverage of All 21 GDPR Rules
This implementation addresses **ALL 21 GDPR compliance rules** as defined in the GDPR_Rules_Guide.md:

**Automated Implementation (16 rules):**
- ✅ Rule 1: HTTPS/TLS Encryption
- ✅ Rule 2: Privacy Policy Link & Presence
- ✅ Rule 3: Privacy Notice Content (automated detection + manual review)
- ✅ Rule 4: Cookie Consent Banner
- ✅ Rule 5: Cookie Classification & Blocking
- ✅ Rule 6: Third-Party Tracker Detection
- ✅ Rule 7: Cookie Attributes
- ✅ Rule 8: Global Privacy Control/Do-Not-Track
- ✅ Rule 9: Data-Collecting Forms & Consent Controls
- ✅ Rule 10: Security Headers (Privacy by Design)
- ✅ Rule 11: IP Address as Personal Data
- ✅ Rule 12: Data Subject Rights Links
- ✅ Rule 13: Special Category Data (automated detection + manual review)
- ✅ Rule 15: Data Protection Officer/EU Representative
- ✅ Rule 16: International Data Transfers (automated detection + manual review)
- ✅ Rule 17: Breach Notification Statement
- ✅ Rule 19: Data Retention Policy
- ✅ Rule 20: Processor/Sub-processor Agreements
- ✅ Rule 21: Imprint & Contact Details

**Manual Review Required (5 rules):**
- 🔍 Rule 14: Children's Data Consent (detection provided for manual review)
- 🔍 Rule 18: Data Protection Impact Assessment (detection provided for manual review)
- 🔍 Rules 3, 16: Legal adequacy aspects require human legal expertise

### Implementation Confidence Levels
- **High Automation (16 rules)**: 76% of rules fully automated
- **Hybrid Approach (3 rules)**: 14% automated detection with manual review flags
- **Manual Review (2 rules)**: 10% require human legal assessment
- **Total Coverage**: 100% of all GDPR requirements addressed

## 2. Module Architecture

### 2.1 Backend Structure
```
backend/src/compliance/gdpr/
├── index.ts                    # Main module exports
├── orchestrator.ts             # GDPR scan orchestrator
├── types.ts                    # TypeScript type definitions
├── constants.ts                # GDPR patterns and configurations
├── checks/                     # Individual check implementations
│   ├── https-tls.ts           # Rule 1: HTTPS/TLS
│   ├── privacy-policy.ts      # Rule 2: Privacy policy presence
│   ├── privacy-content.ts     # Rule 3: Privacy notice content
│   ├── cookie-consent.ts      # Rule 4: Cookie consent banner
│   ├── cookie-classification.ts # Rule 5: Cookie classification
│   ├── tracker-detection.ts   # Rule 6: Third-party trackers
│   ├── cookie-attributes.ts   # Rule 7: Cookie attributes
│   ├── gpc-dnt.ts            # Rule 8: GPC/DNT
│   ├── form-consent.ts       # Rule 9: Form consent controls
│   ├── security-headers.ts   # Rule 10: Security headers
│   ├── ip-anonymization.ts   # Rule 11: IP anonymization
│   ├── data-rights.ts        # Rule 12: Data subject rights
│   ├── special-data.ts       # Rule 13: Special category data
│   ├── children-consent.ts   # Rule 14: Children's consent
│   ├── dpo-contact.ts        # Rule 15: DPO/EU rep
│   ├── data-transfers.ts     # Rule 16: International transfers
│   ├── breach-notification.ts # Rule 17: Breach notification
│   ├── dpia.ts               # Rule 18: DPIA
│   ├── data-retention.ts     # Rule 19: Data retention
│   ├── processor-agreements.ts # Rule 20: Processor agreements
│   └── imprint-contact.ts    # Rule 21: Imprint details
├── utils/                      # Utility functions
│   ├── cookie-analyzer.ts     # Cookie analysis utilities
│   ├── tracker-database.ts   # Known tracker domains
│   ├── consent-detector.ts   # Consent mechanism detection
│   ├── gdpr-patterns.ts      # GDPR-specific text patterns
│   └── legal-analyzer.ts     # Legal text analysis
├── services/                   # Core services
│   ├── gdpr-scanner.ts       # Main scanning service
│   ├── cookie-monitor.ts     # Cookie monitoring service
│   ├── consent-analyzer.ts   # Consent mechanism analyzer
│   └── privacy-analyzer.ts   # Privacy policy analyzer
└── database/                   # Database integration
    └── gdpr-database.ts       # GDPR scan result storage
```

### 2.2 Frontend Structure
```
frontend/
├── app/dashboard/gdpr/         # GDPR dashboard pages
│   ├── page.tsx               # GDPR dashboard home
│   ├── scan/                  # Scanning interface
│   └── results/[scanId]/      # Scan results display
├── components/gdpr/            # GDPR-specific components
│   ├── GdprScanForm.tsx      # Scan configuration form
│   ├── GdprResultsDisplay.tsx # Results visualization
│   ├── CookieAnalysisView.tsx # Cookie analysis display
│   ├── ConsentAnalysisView.tsx # Consent mechanism display
│   └── ComplianceScoreCard.tsx # Score visualization
├── types/gdpr.ts              # Frontend GDPR types
└── services/gdpr-api.ts       # GDPR API client
```

## 3. Technical Dependencies

### 3.1 Core Dependencies
- **Puppeteer**: Browser automation for dynamic analysis
- **Cheerio**: HTML parsing and DOM manipulation
- **compromise.js**: NLP text analysis
- **DistilBERT/LegalBERT**: AI-powered legal text analysis
- **axios**: HTTP client for API requests
- **zod**: Schema validation

### 3.2 New Dependencies
- **cookie-parser**: Enhanced cookie analysis
- **ua-parser-js**: User agent analysis
- **public-suffix-list**: Domain analysis for trackers
- **legal-text-analyzer**: Specialized legal text processing

### 3.3 External Services
- **Tracker Domain Database**: Updated list of known tracking domains
- **Cookie Classification API**: Third-party cookie categorization
- **Legal Pattern Database**: GDPR-specific legal text patterns

## 4. Utility Functions

### 4.1 Cookie Analysis
```typescript
// Strict TypeScript interfaces - NO any[] types allowed
interface Cookie {
  name: string;
  value: string;
  domain: string;
  path: string;
  secure: boolean;
  httpOnly: boolean;
  sameSite: 'Strict' | 'Lax' | 'None' | undefined;
  expires?: Date;
}

interface CookieAnalysisResult {
  cookieId: string;
  category: 'essential' | 'analytics' | 'marketing' | 'functional';
  hasConsent: boolean;
  complianceIssues: string[];
  recommendations: string[];
}

interface CookieClassification {
  essential: Cookie[];
  analytics: Cookie[];
  marketing: Cookie[];
  functional: Cookie[];
  unclassified: Cookie[];
}

interface ConsentState {
  hasValidConsent: boolean;
  consentTimestamp?: Date;
  granularConsent: Record<string, boolean>;
  consentMethod: 'banner' | 'form' | 'implicit' | 'none';
}

interface CookieChangeLog {
  beforeConsent: Cookie[];
  afterConsent: Cookie[];
  newCookies: Cookie[];
  blockedCookies: Cookie[];
}

class CookieAnalyzer {
  analyzeCookieAttributes(cookies: Cookie[]): CookieAnalysisResult[];
  classifyCookies(cookies: Cookie[]): CookieClassification;
  detectConsentState(cookies: Cookie[]): ConsentState;
  monitorCookieChanges(url: string): Promise<CookieChangeLog>;
}
```

### 4.2 Consent Detection
```typescript
// Strict TypeScript interfaces following project rules
interface ConsentBannerInfo {
  isPresent: boolean;
  bannerText: string;
  hasAcceptButton: boolean;
  hasRejectButton: boolean;
  hasGranularOptions: boolean;
  privacyPolicyLinked: boolean;
  complianceIssues: string[];
}

interface ConsentOptions {
  acceptAll: boolean;
  rejectAll: boolean;
  granularChoices: Array<{
    category: string;
    enabled: boolean;
    required: boolean;
  }>;
  savePreferences: boolean;
}

interface ConsentFlowResult {
  flowCompleted: boolean;
  stepsRequired: number;
  stepsCompleted: number;
  cookiesBlockedBeforeConsent: boolean;
  consentPersisted: boolean;
  issues: string[];
}

interface ConsentValidation {
  isValid: boolean;
  hasExplicitConsent: boolean;
  hasWithdrawalMechanism: boolean;
  isGranular: boolean;
  validationErrors: string[];
}

class ConsentDetector {
  detectConsentBanner(page: Page): Promise<ConsentBannerInfo>;
  analyzeConsentOptions(banner: Element): ConsentOptions;
  testConsentFlow(page: Page): Promise<ConsentFlowResult>;
  validateConsentMechanism(form: Element): ConsentValidation;
}
```

### 4.3 Tracker Detection
```typescript
// Precise TypeScript definitions - no any types
interface TrackerDetectionResult {
  trackersFound: Tracker[];
  totalTrackers: number;
  trackersByCategory: Record<TrackerCategory, Tracker[]>;
  loadedBeforeConsent: Tracker[];
  complianceIssues: string[];
}

interface Tracker {
  domain: string;
  name: string;
  category: TrackerCategory;
  purpose: string;
  dataCollected: string[];
  hasConsentMechanism: boolean;
  loadedBeforeConsent: boolean;
}

type TrackerCategory = 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';

interface TrackerClassification {
  category: TrackerCategory;
  confidence: number;
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
  dataTypes: string[];
}

interface TrackingConsentAnalysis {
  totalTrackers: number;
  trackersWithConsent: number;
  trackersWithoutConsent: number;
  complianceScore: number;
  recommendations: string[];
}

class TrackerDetector {
  scanForTrackers(page: Page): Promise<TrackerDetectionResult>;
  classifyTracker(domain: string): TrackerClassification;
  analyzeTrackingConsent(trackers: Tracker[]): TrackingConsentAnalysis;
}
```

### 4.4 Legal Text Analysis
```typescript
// Comprehensive TypeScript interfaces following strict mode
interface GdprSectionExtraction {
  sectionsFound: GdprSection[];
  missingSections: string[];
  extractionConfidence: number;
  analysisMetadata: {
    textLength: number;
    processingTime: number;
    analysisMethod: 'pattern' | 'nlp' | 'ai';
  };
}

interface GdprSection {
  sectionType: GdprSectionType;
  content: string;
  confidence: number;
  location: {
    startIndex: number;
    endIndex: number;
  };
  complianceLevel: 'compliant' | 'partial' | 'non-compliant';
}

type GdprSectionType =
  | 'controller_identity'
  | 'processing_purposes'
  | 'data_categories'
  | 'recipients'
  | 'international_transfers'
  | 'retention_periods'
  | 'data_subject_rights'
  | 'dpo_contact'
  | 'legal_basis';

interface LegalValidation {
  isCompliant: boolean;
  complianceScore: number;
  validationResults: ValidationResult[];
  recommendations: LegalRecommendation[];
}

interface ValidationResult {
  requirement: string;
  status: 'met' | 'partial' | 'missing';
  evidence: string[];
  severity: 'critical' | 'high' | 'medium' | 'low';
}

interface LegalRecommendation {
  priority: number;
  category: string;
  description: string;
  implementation: string;
  effort: 'minimal' | 'moderate' | 'significant';
}

interface MissingElementsReport {
  missingElements: string[];
  criticalMissing: string[];
  recommendations: string[];
  complianceGaps: Array<{
    element: string;
    impact: 'critical' | 'high' | 'medium' | 'low';
    suggestion: string;
  }>;
}

class LegalTextAnalyzer {
  extractGdprSections(text: string): Promise<GdprSectionExtraction>;
  validateLegalRequirements(sections: GdprSection[]): LegalValidation;
  detectMissingElements(text: string): MissingElementsReport;
}
```

## 5. Scoring Algorithm

### 5.1 Risk-Weighted Scoring System
Following HIPAA's proven risk-weighted approach:

```typescript
interface GdprScoringWeights {
  // Critical Requirements (High Weight)
  httpsEncryption: 8;           // Rule 1: Foundation security
  privacyPolicyPresence: 7;     // Rule 2: Basic requirement
  cookieConsent: 9;             // Rule 4: Core GDPR requirement
  cookieClassification: 8;      // Rule 5: Essential for compliance

  // High Priority (Medium-High Weight)
  privacyNoticeContent: 7;      // Rule 3: Content quality
  trackerDetection: 6;          // Rule 6: Third-party compliance
  dataSubjectRights: 7;         // Rule 12: Individual rights

  // Medium Priority (Medium Weight)
  cookieAttributes: 5;          // Rule 7: Security enhancement
  gpcDnt: 4;                    // Rule 8: Privacy signals
  formConsent: 6;               // Rule 9: Data collection
  securityHeaders: 5;           // Rule 10: Technical safeguards
  ipAnonymization: 5;           // Rule 11: Data minimization

  // Standard Requirements (Lower Weight)
  specialCategoryData: 4;       // Rule 13: Conditional requirement
  dpoContact: 3;                // Rule 15: Organizational requirement
  dataTransfers: 5;             // Rule 16: International compliance
  breachNotification: 3;        // Rule 17: Policy statement
  dataRetention: 4;             // Rule 19: Data lifecycle
  processorAgreements: 3;       // Rule 20: Business relationships
  imprintContact: 2;            // Rule 21: Basic information

  // Manual Review Items (Flagged)
  childrenConsent: 0;           // Rule 14: Manual assessment
  dpia: 0;                      // Rule 18: Manual assessment
}
```

### 5.2 Score Calculation Method
```typescript
// Strict TypeScript interfaces - following project rules
interface GdprCheckResult {
  ruleId: GdprRuleId;
  ruleName: string;
  category: GdprCategory;
  passed: boolean;
  score: number;
  weight: number;
  severity: Severity;
  evidence: Evidence[];
  recommendations: Recommendation[];
  manualReviewRequired: boolean;
}

type GdprRuleId =
  | 'GDPR-001' | 'GDPR-002' | 'GDPR-003' | 'GDPR-004' | 'GDPR-005'
  | 'GDPR-006' | 'GDPR-007' | 'GDPR-008' | 'GDPR-009' | 'GDPR-010'
  | 'GDPR-011' | 'GDPR-012' | 'GDPR-013' | 'GDPR-014' | 'GDPR-015'
  | 'GDPR-016' | 'GDPR-017' | 'GDPR-018' | 'GDPR-019' | 'GDPR-020'
  | 'GDPR-021';

type GdprCategory =
  | 'security' | 'privacy_policy' | 'consent' | 'cookies'
  | 'data_rights' | 'data_protection' | 'organizational';

type Severity = 'critical' | 'high' | 'medium' | 'low';
type RiskLevel = 'critical' | 'high' | 'medium' | 'low';

interface Evidence {
  type: 'text' | 'element' | 'network' | 'cookie';
  description: string;
  location?: string;
  value?: string;
}

interface Recommendation {
  priority: number;
  title: string;
  description: string;
  implementation: string;
  effort: 'minimal' | 'moderate' | 'significant';
  impact: 'low' | 'medium' | 'high';
}

interface GdprScoringResult {
  overallScore: number;
  riskLevel: RiskLevel;
  criticalFailures: number;
  breakdown: ScoreBreakdown[];
  summary: {
    totalChecks: number;
    passedChecks: number;
    failedChecks: number;
    manualReviewRequired: number;
  };
}

interface ScoreBreakdown {
  category: GdprCategory;
  score: number;
  weight: number;
  checksInCategory: number;
  passedInCategory: number;
}

// Zod validation schema for request validation
import { z } from 'zod';

const GdprScoringWeightsSchema = z.object({
  httpsEncryption: z.number().min(0).max(10),
  privacyPolicyPresence: z.number().min(0).max(10),
  cookieConsent: z.number().min(0).max(10),
  cookieClassification: z.number().min(0).max(10),
  privacyNoticeContent: z.number().min(0).max(10),
  trackerDetection: z.number().min(0).max(10),
  dataSubjectRights: z.number().min(0).max(10),
  cookieAttributes: z.number().min(0).max(10),
  gpcDnt: z.number().min(0).max(10),
  formConsent: z.number().min(0).max(10),
  securityHeaders: z.number().min(0).max(10),
  ipAnonymization: z.number().min(0).max(10),
  specialCategoryData: z.number().min(0).max(10),
  dpoContact: z.number().min(0).max(10),
  dataTransfers: z.number().min(0).max(10),
  breachNotification: z.number().min(0).max(10),
  dataRetention: z.number().min(0).max(10),
  processorAgreements: z.number().min(0).max(10),
  imprintContact: z.number().min(0).max(10),
  childrenConsent: z.number().min(0).max(10),
  dpia: z.number().min(0).max(10),
});

function calculateGdprScore(checks: GdprCheckResult[]): GdprScoringResult {
  const weights = GDPR_SCORING_WEIGHTS;
  let totalWeightedScore = 0;
  let totalWeight = 0;
  let criticalFailures = 0;
  let passedChecks = 0;
  let manualReviewRequired = 0;

  for (const check of checks) {
    const weight = weights[check.ruleId] || 0;
    const score = check.passed ? 100 : 0;

    totalWeightedScore += score * weight;
    totalWeight += weight;

    if (check.passed) {
      passedChecks++;
    }

    if (check.manualReviewRequired) {
      manualReviewRequired++;
    }

    if (!check.passed && weight >= 7) {
      criticalFailures++;
    }
  }

  const baseScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;

  // Apply critical failure penalty
  const criticalPenalty = criticalFailures * 15;
  const finalScore = Math.max(0, baseScore - criticalPenalty);

  return {
    overallScore: Math.round(finalScore),
    riskLevel: determineRiskLevel(finalScore, criticalFailures),
    criticalFailures,
    breakdown: generateScoreBreakdown(checks, weights),
    summary: {
      totalChecks: checks.length,
      passedChecks,
      failedChecks: checks.length - passedChecks,
      manualReviewRequired,
    },
  };
}

function generateScoreBreakdown(
  checks: GdprCheckResult[],
  weights: Record<string, number>
): ScoreBreakdown[] {
  const categoryMap = new Map<GdprCategory, {
    totalScore: number;
    totalWeight: number;
    totalChecks: number;
    passedChecks: number;
  }>();

  for (const check of checks) {
    const existing = categoryMap.get(check.category) || {
      totalScore: 0,
      totalWeight: 0,
      totalChecks: 0,
      passedChecks: 0,
    };

    const weight = weights[check.ruleId] || 0;
    const score = check.passed ? 100 : 0;

    existing.totalScore += score * weight;
    existing.totalWeight += weight;
    existing.totalChecks++;
    if (check.passed) existing.passedChecks++;

    categoryMap.set(check.category, existing);
  }

  return Array.from(categoryMap.entries()).map(([category, data]) => ({
    category,
    score: data.totalWeight > 0 ? Math.round(data.totalScore / data.totalWeight) : 0,
    weight: data.totalWeight,
    checksInCategory: data.totalChecks,
    passedInCategory: data.passedChecks,
  }));
}
```

### 5.3 Risk Level Determination
```typescript
function determineRiskLevel(score: number, criticalFailures: number): RiskLevel {
  if (criticalFailures >= 3 || score < 40) return 'critical';
  if (criticalFailures >= 2 || score < 60) return 'high';
  if (criticalFailures >= 1 || score < 75) return 'medium';
  return 'low';
}
```

## 6. Database Schema

### 6.1 Core Tables

#### **gdpr_scans**
```sql
CREATE TABLE gdpr_scans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  target_url VARCHAR(2048) NOT NULL,
  scan_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  scan_duration INTEGER, -- milliseconds
  overall_score DECIMAL(5,2),
  risk_level VARCHAR(20) CHECK (risk_level IN ('critical', 'high', 'medium', 'low')),
  total_checks INTEGER NOT NULL,
  passed_checks INTEGER NOT NULL,
  failed_checks INTEGER NOT NULL,
  manual_review_required INTEGER DEFAULT 0,
  scan_status VARCHAR(20) DEFAULT 'pending'
    CHECK (scan_status IN ('pending', 'running', 'completed', 'failed')),
  error_message TEXT,
  metadata JSONB, -- scan configuration and technical details
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **gdpr_check_results**
```sql
CREATE TABLE gdpr_check_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scan_id UUID NOT NULL REFERENCES gdpr_scans(id) ON DELETE CASCADE,
  rule_id VARCHAR(50) NOT NULL, -- e.g., 'GDPR-001', 'GDPR-002'
  rule_name VARCHAR(200) NOT NULL,
  category VARCHAR(100) NOT NULL, -- e.g., 'consent', 'privacy_policy', 'cookies'
  passed BOOLEAN NOT NULL,
  score DECIMAL(5,2),
  weight DECIMAL(3,2),
  severity VARCHAR(20) CHECK (severity IN ('critical', 'high', 'medium', 'low')),
  manual_review_required BOOLEAN DEFAULT FALSE,
  evidence JSONB, -- detailed findings and evidence
  recommendations JSONB, -- improvement suggestions
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **gdpr_cookie_analysis**
```sql
CREATE TABLE gdpr_cookie_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scan_id UUID NOT NULL REFERENCES gdpr_scans(id) ON DELETE CASCADE,
  cookie_name VARCHAR(255) NOT NULL,
  cookie_domain VARCHAR(255) NOT NULL,
  cookie_category VARCHAR(50), -- essential, analytics, marketing, etc.
  has_consent BOOLEAN,
  secure_flag BOOLEAN,
  httponly_flag BOOLEAN,
  samesite_attribute VARCHAR(20),
  expiry_date TIMESTAMP WITH TIME ZONE,
  purpose TEXT,
  third_party BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **gdpr_consent_analysis**
```sql
CREATE TABLE gdpr_consent_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scan_id UUID NOT NULL REFERENCES gdpr_scans(id) ON DELETE CASCADE,
  consent_type VARCHAR(50), -- banner, form, checkbox
  consent_mechanism VARCHAR(100), -- opt-in, opt-out, granular
  has_reject_option BOOLEAN,
  has_granular_options BOOLEAN,
  pre_ticked_boxes BOOLEAN,
  consent_text TEXT,
  privacy_policy_linked BOOLEAN,
  compliant BOOLEAN,
  issues JSONB, -- array of compliance issues
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **gdpr_tracker_analysis**
```sql
CREATE TABLE gdpr_tracker_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scan_id UUID NOT NULL REFERENCES gdpr_scans(id) ON DELETE CASCADE,
  tracker_domain VARCHAR(255) NOT NULL,
  tracker_type VARCHAR(100), -- analytics, advertising, social, etc.
  tracker_name VARCHAR(255),
  loads_before_consent BOOLEAN,
  has_consent_mechanism BOOLEAN,
  data_transferred TEXT, -- description of data being transferred
  privacy_policy_mentioned BOOLEAN,
  compliant BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 6.2 Indexes and Performance
```sql
-- Performance indexes
CREATE INDEX idx_gdpr_scans_user_id ON gdpr_scans(user_id);
CREATE INDEX idx_gdpr_scans_timestamp ON gdpr_scans(scan_timestamp);
CREATE INDEX idx_gdpr_scans_status ON gdpr_scans(scan_status);
CREATE INDEX idx_gdpr_check_results_scan_id ON gdpr_check_results(scan_id);
CREATE INDEX idx_gdpr_check_results_rule_id ON gdpr_check_results(rule_id);
CREATE INDEX idx_gdpr_cookie_analysis_scan_id ON gdpr_cookie_analysis(scan_id);
CREATE INDEX idx_gdpr_consent_analysis_scan_id ON gdpr_consent_analysis(scan_id);
CREATE INDEX idx_gdpr_tracker_analysis_scan_id ON gdpr_tracker_analysis(scan_id);
```

## 7. API Endpoints

### 7.1 Core GDPR Endpoints
```typescript
// POST /api/v1/compliance/gdpr/scan
interface GdprScanRequest {
  targetUrl: string;
  scanOptions?: {
    enableCookieAnalysis?: boolean;
    enableTrackerDetection?: boolean;
    enableConsentTesting?: boolean;
    maxPages?: number;
    timeout?: number;
  };
}

// GET /api/v1/compliance/gdpr/scan/:scanId
interface GdprScanResponse {
  success: boolean;
  data: {
    scanId: string;
    targetUrl: string;
    status: ScanStatus;
    overallScore: number;
    riskLevel: RiskLevel;
    summary: GdprScanSummary;
    checks: GdprCheckResult[];
    recommendations: GdprRecommendation[];
    metadata: GdprScanMetadata;
  };
}

// GET /api/v1/compliance/gdpr/scans
// POST /api/v1/compliance/gdpr/scan/:scanId/export
// DELETE /api/v1/compliance/gdpr/scan/:scanId
```

### 7.2 Specialized Analysis Endpoints
```typescript
// GET /api/v1/compliance/gdpr/scan/:scanId/cookies
// GET /api/v1/compliance/gdpr/scan/:scanId/consent
// GET /api/v1/compliance/gdpr/scan/:scanId/trackers
// GET /api/v1/compliance/gdpr/scan/:scanId/privacy-policy
```

## 8. Frontend Components

### 8.1 Core Components

#### **GdprScanForm.tsx**
```typescript
interface GdprScanFormProps {
  onScanStart: (config: GdprScanConfig) => void;
  isScanning: boolean;
}

// Features:
// - URL validation with GDPR-specific checks
// - Scan option configuration (cookies, trackers, consent)
// - Real-time validation feedback
// - Accessibility compliance (WCAG AA)
```

#### **GdprResultsDisplay.tsx**
```typescript
interface GdprResultsDisplayProps {
  scanResult: GdprScanResult;
  onExport: () => void;
  onRescan: () => void;
}

// Features:
// - Overall compliance score with risk level indicator
// - Rule-by-rule breakdown with pass/fail status
// - Detailed evidence and recommendations
// - Interactive charts and visualizations
// - Export functionality (PDF, JSON)
```

#### **CookieAnalysisView.tsx**
```typescript
interface CookieAnalysisViewProps {
  cookieData: CookieAnalysisResult[];
  consentData: ConsentAnalysisResult;
}

// Features:
// - Cookie categorization display
// - Consent mechanism analysis
// - Before/after consent comparison
// - Cookie attribute compliance
// - Third-party cookie identification
```

#### **ConsentAnalysisView.tsx**
```typescript
interface ConsentAnalysisViewProps {
  consentAnalysis: ConsentAnalysisResult;
  recommendations: ConsentRecommendation[];
}

// Features:
// - Consent banner analysis
// - Consent flow visualization
// - Granular consent options review
// - Compliance gap identification
// - Implementation recommendations
```

### 8.2 Shared Components Integration
- Reuse `ComplianceMetrics` from HIPAA dashboard
- Adapt `RiskLevelIndicator` for GDPR risk levels
- Extend `ScanStatusBadge` for GDPR scan states
- Utilize existing theme system and design tokens

## 9. Integration Points

### 9.1 Unified Compliance Dashboard
```typescript
// Integration with existing dashboard structure
app/dashboard/
├── page.tsx                    # Main dashboard with all compliance modules
├── hipaa/                      # Existing HIPAA module
├── gdpr/                       # New GDPR module
├── wcag/                       # Future WCAG module
└── ada/                        # Future ADA module

// Shared dashboard components
components/dashboard/shared/
├── ComplianceOverview.tsx      # Multi-standard overview
├── ScanHistory.tsx            # Cross-standard scan history
├── ComplianceMetrics.tsx      # Unified metrics display
└── RecentActivity.tsx         # Activity across all standards
```

### 9.2 Backend Integration
```typescript
// ScanService integration
class ScanService {
  async performGdprScan(config: GdprScanConfig): Promise<GdprScanResult> {
    const orchestrator = new GdprOrchestrator();
    return await orchestrator.performComprehensiveScan(config);
  }
}

// Route integration
app.use('/api/v1/compliance/gdpr', gdprRoutes);
```

### 9.3 Database Integration
- Extend existing `scans` table with GDPR scan type
- Maintain separate specialized tables for GDPR-specific data
- Ensure consistent UUID patterns and foreign key relationships
- Implement proper indexing for performance

## 10. Testing Strategy

### 10.1 Unit Tests
```typescript
// Test structure following HIPAA patterns
backend/src/compliance/gdpr/__tests__/
├── checks/
│   ├── cookie-consent.test.ts
│   ├── privacy-policy.test.ts
│   └── tracker-detection.test.ts
├── utils/
│   ├── cookie-analyzer.test.ts
│   └── consent-detector.test.ts
├── services/
│   └── gdpr-scanner.test.ts
└── orchestrator.test.ts
```

### 10.2 Integration Tests
```typescript
// End-to-end testing scenarios
describe('GDPR Compliance Scanning', () => {
  test('Complete GDPR scan workflow', async () => {
    // Test full scan from API request to database storage
  });

  test('Cookie consent flow testing', async () => {
    // Test cookie consent banner interaction
  });

  test('Privacy policy content analysis', async () => {
    // Test 3-level privacy policy analysis
  });
});
```

### 10.3 Frontend Testing
```typescript
// Component testing with React Testing Library
frontend/components/gdpr/__tests__/
├── GdprScanForm.test.tsx
├── GdprResultsDisplay.test.tsx
├── CookieAnalysisView.test.tsx
└── ConsentAnalysisView.test.tsx
```

### 10.4 Performance Testing
- Load testing with multiple concurrent GDPR scans
- Cookie monitoring performance under heavy traffic
- Database query optimization validation
- Memory usage monitoring for browser automation

## 11. Performance Considerations

### 11.1 Optimization Strategies
- **Parallel Processing**: Run independent checks concurrently
- **Caching**: Cache privacy policy content and cookie analysis
- **Database Optimization**: Proper indexing and query optimization
- **Browser Resource Management**: Efficient Puppeteer instance management
- **Rate Limiting**: Prevent overwhelming target websites

### 11.2 Scalability Measures
- **Queue System**: Implement scan queue for high-volume processing
- **Microservice Architecture**: Separate cookie monitoring service
- **Database Sharding**: Partition scan data by date/user
- **CDN Integration**: Cache static analysis results
- **Monitoring**: Comprehensive performance monitoring and alerting

## 12. Implementation Timeline

### Phase 1: Foundation (Week 1-2)
- Database schema implementation
- Core types and interfaces
- Basic orchestrator structure
- API endpoint skeleton

### Phase 2: Core Checks (Week 3-5)
- Implement automated checks (Rules 1-13, 15, 17, 19-21)
- Cookie analysis and consent detection
- Privacy policy content analysis
- Basic scoring algorithm

### Phase 3: Advanced Features (Week 6-7)
- Tracker detection and analysis
- Advanced consent flow testing
- Manual review flagging system
- Comprehensive reporting

### Phase 4: Frontend Integration (Week 8-9)
- GDPR dashboard components
- Scan configuration interface
- Results visualization
- Export functionality

### Phase 5: Testing & Optimization (Week 10)
- Comprehensive testing suite
- Performance optimization
- Documentation completion
- Production deployment preparation

## Conclusion

This blueprint provides a comprehensive roadmap for implementing GDPR compliance functionality that maintains consistency with the existing HIPAA module while addressing the unique requirements of GDPR. The modular architecture ensures scalability and maintainability, while the risk-weighted scoring system provides meaningful compliance insights for users.

The implementation follows established patterns from the HIPAA module, ensuring consistency in code organization, API design, and user experience while adapting to the specific technical and legal requirements of GDPR compliance checking.
```
