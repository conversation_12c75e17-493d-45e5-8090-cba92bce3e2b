import { Router, Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { GdprOrchestrator } from '../../compliance/gdpr/orchestrator';
import { GdprScanRequestSchema } from '../../compliance/gdpr/types';
import { InputValidator, AuditLogger } from '../../config/security';
import { GdprDatabase } from '../../compliance/gdpr/database/gdpr-database';

// Import express-validator functions directly
const expressValidator = require('express-validator');
const { body, param, query, validationResult } = expressValidator;

const router = Router();
const orchestrator = new GdprOrchestrator();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Security validation middleware
const validateSecurity = (req: Request, res: Response, next: NextFunction) => {
  const { targetUrl } = req.body;

  if (!InputValidator.isValidUrl(targetUrl)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid URL',
      message: 'The provided URL is not valid or is blocked for security reasons',
    });
  }

  next();
};

/**
 * @openapi
 * tags:
 *   name: GDPR Compliance
 *   description: Routes for GDPR specific compliance checks and operations.
 */

/**
 * @openapi
 * /api/v1/compliance/gdpr/scan:
 *   post:
 *     summary: Start a comprehensive GDPR compliance scan
 *     tags: [GDPR Compliance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - targetUrl
 *             properties:
 *               targetUrl:
 *                 type: string
 *                 format: uri
 *                 description: The URL to scan for GDPR compliance
 *               scanOptions:
 *                 type: object
 *                 properties:
 *                   enableCookieAnalysis:
 *                     type: boolean
 *                     default: true
 *                   enableTrackerDetection:
 *                     type: boolean
 *                     default: true
 *                   enableConsentTesting:
 *                     type: boolean
 *                     default: true
 *                   maxPages:
 *                     type: integer
 *                     minimum: 1
 *                     maximum: 50
 *                     default: 10
 *                   timeout:
 *                     type: integer
 *                     minimum: 60000
 *                     maximum: 3600000
 *                     default: 300000
 *     responses:
 *       200:
 *         description: GDPR scan completed successfully
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/scan', [
  // Input validation
  body('targetUrl')
    .isURL({ protocols: ['http', 'https'], require_protocol: true })
    .withMessage('Valid URL is required')
    .isLength({ max: 2048 })
    .withMessage('URL must be less than 2048 characters'),

  body('scanOptions.enableCookieAnalysis')
    .optional()
    .isBoolean()
    .withMessage('enableCookieAnalysis must be a boolean'),

  body('scanOptions.enableTrackerDetection')
    .optional()
    .isBoolean()
    .withMessage('enableTrackerDetection must be a boolean'),

  body('scanOptions.enableConsentTesting')
    .optional()
    .isBoolean()
    .withMessage('enableConsentTesting must be a boolean'),

  body('scanOptions.maxPages')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('maxPages must be between 1 and 50'),

  body('scanOptions.timeout')
    .optional()
    .isInt({ min: 60000, max: 3600000 })
    .withMessage('timeout must be between 60000 and 3600000 milliseconds'),

  // Security validation
  validateRequest,
  validateSecurity,
], async (req: Request, res: Response) => {
  const requestId = uuidv4();
  const startTime = Date.now();

  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.warn(`⚠️ [${requestId}] GDPR scan validation failed:`, errors.array());
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request parameters',
        details: errors.array(),
        requestId
      });
    }

    // Parse and validate request with Zod
    const parseResult = GdprScanRequestSchema.safeParse(req.body);
    if (!parseResult.success) {
      console.warn(`⚠️ [${requestId}] GDPR scan Zod validation failed:`, parseResult.error);
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request schema',
        details: parseResult.error.errors,
        requestId
      });
    }

    const scanRequest = parseResult.data;
    const userId = (req as any).user?.id || 'anonymous';

    console.log(`🚀 [${requestId}] Starting GDPR compliance scan:`, {
      targetUrl: scanRequest.targetUrl,
      userId,
      scanOptions: scanRequest.scanOptions
    });

    // Log audit trail
    AuditLogger.logSecurityEvent('GDPR_SCAN_INITIATED', {
      userId,
      resource: scanRequest.targetUrl,
      requestId,
      scanOptions: scanRequest.scanOptions
    }, req);

    // Execute GDPR compliance scan
    const result = await orchestrator.performComprehensiveScan(userId, scanRequest);

    const processingTime = Date.now() - startTime;

    console.log(`✅ [${requestId}] GDPR scan completed successfully:`, {
      scanId: result.scanId,
      overallScore: result.overallScore,
      riskLevel: result.riskLevel,
      processingTime: `${processingTime}ms`
    });

    // Log successful completion
    AuditLogger.logSecurityEvent('GDPR_SCAN_COMPLETED', {
      userId,
      resource: scanRequest.targetUrl,
      requestId,
      scanId: result.scanId,
      overallScore: result.overallScore,
      riskLevel: result.riskLevel,
      processingTime
    }, req);

    res.json({
      success: true,
      data: result,
      requestId,
      processingTime
    });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error(`❌ [${requestId}] GDPR scan failed:`, error);

    // Log error
    AuditLogger.logSecurityEvent('GDPR_SCAN_FAILED', {
      userId: (req as any).user?.id || 'anonymous',
      resource: req.body.targetUrl || 'unknown',
      requestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      processingTime
    }, req);

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'GDPR compliance scan failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
});

/**
 * @openapi
 * /api/v1/compliance/gdpr/scan/{scanId}:
 *   get:
 *     summary: Get GDPR scan result by ID
 *     tags: [GDPR Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: scanId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The scan ID to retrieve
 *     responses:
 *       200:
 *         description: Scan result retrieved successfully
 *       404:
 *         description: Scan not found
 *       500:
 *         description: Internal server error
 */
router.get('/scan/:scanId', [
  param('scanId')
    .isUUID()
    .withMessage('Valid scan ID is required'),
], async (req: Request, res: Response) => {
  const requestId = uuidv4();

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid scan ID',
        details: errors.array(),
        requestId
      });
    }

    const { scanId } = req.params;
    console.log(`🔍 [${requestId}] Retrieving GDPR scan result: ${scanId}`);

    const result = await GdprDatabase.getScanResult(scanId);

    if (!result) {
      console.warn(`⚠️ [${requestId}] GDPR scan not found: ${scanId}`);
      return res.status(404).json({
        success: false,
        error: 'NOT_FOUND',
        message: 'Scan result not found',
        requestId
      });
    }

    console.log(`✅ [${requestId}] GDPR scan result retrieved successfully: ${scanId}`);

    res.json({
      success: true,
      data: result,
      requestId
    });

  } catch (error) {
    console.error(`❌ [${requestId}] Failed to retrieve GDPR scan result:`, error);

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Failed to retrieve scan result',
      details: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
});

/**
 * @openapi
 * /api/v1/compliance/gdpr/scans:
 *   get:
 *     summary: Get user's GDPR scan history
 *     tags: [GDPR Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of scans to retrieve
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of scans to skip
 *     responses:
 *       200:
 *         description: Scan history retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/scans', [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Offset must be 0 or greater'),
], async (req: Request, res: Response) => {
  const requestId = uuidv4();

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid query parameters',
        details: errors.array(),
        requestId
      });
    }

    const userId = (req as any).user?.id || 'anonymous';
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = parseInt(req.query.offset as string) || 0;

    console.log(`📋 [${requestId}] Retrieving GDPR scan history for user: ${userId}`);

    const scans = await GdprDatabase.getUserScans(userId, limit, offset);

    console.log(`✅ [${requestId}] Retrieved ${scans.length} GDPR scans for user: ${userId}`);

    res.json({
      success: true,
      data: {
        scans,
        pagination: {
          limit,
          offset,
          total: scans.length
        }
      },
      requestId
    });

  } catch (error) {
    console.error(`❌ [${requestId}] Failed to retrieve GDPR scan history:`, error);

    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Failed to retrieve scan history',
      details: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
});

export default router;
