/**
 * GDPR API Service
 * 
 * Handles all API calls related to GDPR compliance scanning.
 * Follows the same pattern as HIPAA API services for consistency.
 */

import { api } from '@/lib/api';
import { 
  GdprScanRequest, 
  GdprScanResult, 
  GdprScanResponse,
  GdprScanListResponse,
  ApiResponse 
} from '@/types/gdpr';

const GDPR_API_BASE = '/api/v1/compliance/gdpr';

export class GdprApiService {
  /**
   * Start a new GDPR compliance scan
   */
  static async startScan(request: GdprScanRequest): Promise<GdprScanResult> {
    try {
      console.log('🚀 Starting GDPR scan for:', request.targetUrl);
      
      const response = await api.post<GdprScanResponse>(`${GDPR_API_BASE}/scan`, request);
      
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to start GDPR scan');
      }
      
      console.log('✅ GDPR scan started successfully:', response.data.data.scanId);
      return response.data.data;
    } catch (error) {
      console.error('❌ Error starting GDPR scan:', error);
      throw error;
    }
  }

  /**
   * Get GDPR scan result by ID
   */
  static async getScanResult(scanId: string): Promise<GdprScanResult> {
    try {
      console.log('📊 Fetching GDPR scan result:', scanId);
      
      const response = await api.get<GdprScanResponse>(`${GDPR_API_BASE}/scan/${scanId}`);
      
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to fetch GDPR scan result');
      }
      
      return response.data.data;
    } catch (error) {
      console.error('❌ Error fetching GDPR scan result:', error);
      throw error;
    }
  }

  /**
   * Get user's GDPR scan history
   */
  static async getScanHistory(limit = 10): Promise<GdprScanResult[]> {
    try {
      console.log('📋 Fetching GDPR scan history...');
      
      const response = await api.get<GdprScanListResponse>(
        `${GDPR_API_BASE}/scans?limit=${limit}`
      );
      
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to fetch GDPR scan history');
      }
      
      return response.data.data;
    } catch (error) {
      console.error('❌ Error fetching GDPR scan history:', error);
      throw error;
    }
  }

  /**
   * Delete a GDPR scan
   */
  static async deleteScan(scanId: string): Promise<void> {
    try {
      console.log('🗑️ Deleting GDPR scan:', scanId);
      
      const response = await api.delete<ApiResponse<void>>(`${GDPR_API_BASE}/scan/${scanId}`);
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to delete GDPR scan');
      }
      
      console.log('✅ GDPR scan deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting GDPR scan:', error);
      throw error;
    }
  }

  /**
   * Get GDPR dashboard data
   */
  static async getDashboardData(): Promise<any> {
    try {
      console.log('📊 Fetching GDPR dashboard data...');
      
      const response = await api.get<ApiResponse<any>>(`${GDPR_API_BASE}/dashboard`);
      
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to fetch GDPR dashboard data');
      }
      
      return response.data.data;
    } catch (error) {
      console.error('❌ Error fetching GDPR dashboard data:', error);
      throw error;
    }
  }

  /**
   * Export GDPR scan results
   */
  static async exportScanResults(scanId: string, format: 'pdf' | 'json' | 'csv' = 'pdf'): Promise<Blob> {
    try {
      console.log('📄 Exporting GDPR scan results:', scanId, format);
      
      const response = await api.get(`${GDPR_API_BASE}/scan/${scanId}/export?format=${format}`, {
        responseType: 'blob'
      });
      
      return response.data;
    } catch (error) {
      console.error('❌ Error exporting GDPR scan results:', error);
      throw error;
    }
  }

  /**
   * Get GDPR compliance recommendations
   */
  static async getRecommendations(scanId: string): Promise<any[]> {
    try {
      console.log('💡 Fetching GDPR recommendations for scan:', scanId);
      
      const response = await api.get<ApiResponse<any[]>>(`${GDPR_API_BASE}/scan/${scanId}/recommendations`);
      
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to fetch GDPR recommendations');
      }
      
      return response.data.data;
    } catch (error) {
      console.error('❌ Error fetching GDPR recommendations:', error);
      throw error;
    }
  }

  /**
   * Validate URL for GDPR scanning
   */
  static async validateUrl(url: string): Promise<{ valid: boolean; message?: string }> {
    try {
      console.log('🔍 Validating URL for GDPR scan:', url);
      
      const response = await api.post<ApiResponse<{ valid: boolean; message?: string }>>(
        `${GDPR_API_BASE}/validate-url`,
        { url }
      );
      
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to validate URL');
      }
      
      return response.data.data;
    } catch (error) {
      console.error('❌ Error validating URL:', error);
      throw error;
    }
  }

  /**
   * Get GDPR scan status (for polling during scan)
   */
  static async getScanStatus(scanId: string): Promise<{ status: string; progress?: number }> {
    try {
      const response = await api.get<ApiResponse<{ status: string; progress?: number }>>(
        `${GDPR_API_BASE}/scan/${scanId}/status`
      );
      
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to get scan status');
      }
      
      return response.data.data;
    } catch (error) {
      console.error('❌ Error getting GDPR scan status:', error);
      throw error;
    }
  }

  /**
   * Cancel a running GDPR scan
   */
  static async cancelScan(scanId: string): Promise<void> {
    try {
      console.log('⏹️ Cancelling GDPR scan:', scanId);
      
      const response = await api.post<ApiResponse<void>>(`${GDPR_API_BASE}/scan/${scanId}/cancel`);
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to cancel GDPR scan');
      }
      
      console.log('✅ GDPR scan cancelled successfully');
    } catch (error) {
      console.error('❌ Error cancelling GDPR scan:', error);
      throw error;
    }
  }
}

// Export default instance
export default GdprApiService;
