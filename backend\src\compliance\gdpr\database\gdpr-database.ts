/**
 * GDPR Database Service
 *
 * Handles all database operations for GDPR compliance scans.
 * Follows the same pattern as HIPAA database service for consistency.
 * NO MOCK DATA - stores real scan results only
 */

import db from '../../../lib/db';
import { env } from '@lib/env';
import { v4 as uuidv4 } from 'uuid';
import { Knex } from 'knex';
import {
  GdprScanResult,
  GdprScanEntity,
  GdprCheckResultEntity,
  GdprCheckResult,
  GdprScanMetadata,
  RiskLevel,
  ScanStatus,
  GdprCategory,
  CategoryBreakdown,
} from '../types';

export interface GdprScanConfig {
  userId: string;
  targetUrl: string;
  scanOptions: Record<string, unknown>;
}

export interface GdprScanUpdateData {
  overallScore: number;
  riskLevel: RiskLevel;
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  manualReviewRequired: number;
  scanDuration: number;
  status: ScanStatus;
}

export class GdprDatabase {
  /**
   * Test database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔌 Testing GDPR database connection...');
      console.log('📋 Connection string (masked):', env.DATABASE_URL?.replace(/:[^:@]*@/, ':***@'));

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Database connection timeout after 10 seconds')), 10000);
      });

      const testPromise = db.raw('SELECT 1 as test');
      
      await Promise.race([testPromise, timeoutPromise]);
      
      console.log('✅ GDPR database connection successful');
      return true;
    } catch (error) {
      console.error('❌ GDPR database connection failed:', error);
      return false;
    }
  }

  /**
   * Check if GDPR tables exist
   */
  async checkTableExists(): Promise<boolean> {
    try {
      const tableExists = await db.schema.hasTable('gdpr_scans');
      console.log(`📋 GDPR tables exist: ${tableExists}`);
      return tableExists;
    } catch (error) {
      console.error('❌ Error checking GDPR table existence:', error);
      return false;
    }
  }

  /**
   * Create a new GDPR scan record
   */
  async createScan(config: GdprScanConfig): Promise<string> {
    try {
      console.log('📝 Creating GDPR scan record...');
      
      const [scan] = await db('gdpr_scans')
        .insert({
          user_id: config.userId,
          target_url: config.targetUrl,
          scan_timestamp: new Date(),
          total_checks: 0,
          passed_checks: 0,
          failed_checks: 0,
          manual_review_required: 0,
          scan_status: 'pending',
          metadata: config.scanOptions
        })
        .returning('id');

      const scanId = typeof scan === 'object' ? scan.id : scan;
      console.log(`✅ GDPR scan record created with ID: ${scanId}`);
      return scanId;
    } catch (error) {
      console.error('❌ Error creating GDPR scan record:', error);
      throw error;
    }
  }

  /**
   * Update scan status
   */
  async updateScanStatus(
    scanId: string, 
    status: ScanStatus, 
    errorMessage?: string
  ): Promise<void> {
    try {
      console.log(`📝 Updating GDPR scan ${scanId} status to: ${status}`);
      
      const updateData: Partial<GdprScanEntity> = {
        scan_status: status,
        updated_at: new Date()
      };

      if (errorMessage) {
        updateData.error_message = errorMessage;
      }

      await db('gdpr_scans')
        .where('id', scanId)
        .update(updateData);

      console.log(`✅ GDPR scan status updated successfully`);
    } catch (error) {
      console.error('❌ Error updating GDPR scan status:', error);
      throw error;
    }
  }

  /**
   * Update scan with final results
   */
  async updateScanResults(scanId: string, data: GdprScanUpdateData): Promise<void> {
    try {
      console.log(`📝 Updating GDPR scan ${scanId} with final results...`);
      
      await db('gdpr_scans')
        .where('id', scanId)
        .update({
          scan_duration: data.scanDuration,
          overall_score: data.overallScore,
          risk_level: data.riskLevel,
          total_checks: data.totalChecks,
          passed_checks: data.passedChecks,
          failed_checks: data.failedChecks,
          manual_review_required: data.manualReviewRequired,
          scan_status: data.status,
          updated_at: new Date()
        });

      console.log(`✅ GDPR scan results updated successfully`);
    } catch (error) {
      console.error('❌ Error updating GDPR scan results:', error);
      throw error;
    }
  }

  /**
   * Store individual check results
   */
  async storeCheckResults(scanId: string, checkResults: GdprCheckResult[]): Promise<void> {
    try {
      console.log(`📝 Storing ${checkResults.length} GDPR check results...`);
      
      const checkEntities = checkResults.map(result => ({
        scan_id: scanId,
        rule_id: result.ruleId,
        rule_name: result.ruleName,
        category: result.category,
        passed: result.passed,
        score: result.score,
        weight: result.weight,
        severity: result.severity,
        manual_review_required: result.manualReviewRequired,
        evidence: result.evidence,
        recommendations: result.recommendations,
        created_at: new Date()
      }));

      await db('gdpr_check_results').insert(checkEntities);
      
      console.log(`✅ GDPR check results stored successfully`);
    } catch (error) {
      console.error('❌ Error storing GDPR check results:', error);
      throw error;
    }
  }

  /**
   * Get scan by ID
   */
  async getScanById(scanId: string): Promise<GdprScanEntity | null> {
    try {
      const scan = await db('gdpr_scans')
        .where('id', scanId)
        .first();

      return scan || null;
    } catch (error) {
      console.error('❌ Error fetching GDPR scan:', error);
      throw error;
    }
  }

  /**
   * Get check results for a scan
   */
  async getCheckResults(scanId: string): Promise<GdprCheckResultEntity[]> {
    try {
      const results = await db('gdpr_check_results')
        .where('scan_id', scanId)
        .orderBy('created_at', 'asc');

      return results;
    } catch (error) {
      console.error('❌ Error fetching GDPR check results:', error);
      throw error;
    }
  }

  /**
   * Get scans for a user
   */
  async getUserScans(userId: string, limit = 10): Promise<GdprScanEntity[]> {
    try {
      const scans = await db('gdpr_scans')
        .where('user_id', userId)
        .orderBy('scan_timestamp', 'desc')
        .limit(limit);

      return scans;
    } catch (error) {
      console.error('❌ Error fetching user GDPR scans:', error);
      throw error;
    }
  }

  /**
   * Save complete GDPR scan result to database
   * NO MOCK DATA - stores real scan results only
   */
  static async saveScanResult(
    userId: string,
    scanResult: GdprScanResult
  ): Promise<string> {
    const scanId = uuidv4();

    try {
      await db.transaction(async (trx: Knex.Transaction) => {
        // Insert main scan record
        await trx('gdpr_scans').insert({
          id: scanId,
          user_id: userId,
          target_url: scanResult.targetUrl,
          scan_timestamp: new Date(scanResult.timestamp),
          scan_duration: scanResult.scanDuration,
          overall_score: scanResult.overallScore,
          risk_level: scanResult.riskLevel,
          total_checks: scanResult.summary.totalChecks,
          passed_checks: scanResult.summary.passedChecks,
          failed_checks: scanResult.summary.failedChecks,
          manual_review_required: scanResult.summary.manualReviewRequired,
          scan_status: scanResult.status,
          metadata: scanResult.metadata,
        });

        // Insert check results
        const checkResults = scanResult.checks.map((check) => ({
          id: uuidv4(),
          scan_id: scanId,
          rule_id: check.ruleId,
          rule_name: check.ruleName,
          category: check.category,
          passed: check.passed,
          score: check.score,
          weight: check.weight,
          severity: check.severity,
          manual_review_required: check.manualReviewRequired,
          evidence: check.evidence,
          recommendations: check.recommendations,
        }));

        await trx('gdpr_check_results').insert(checkResults);
      });

      console.log(`✅ GDPR scan results saved to database: ${scanId}`);
      return scanId;
    } catch (error) {
      console.error('❌ Failed to save GDPR scan results:', error);
      throw new Error(`Database save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Retrieve GDPR scan result by ID
   */
  static async getScanResult(scanId: string): Promise<GdprScanResult | null> {
    try {
      const scanRecord = await db('gdpr_scans')
        .where('id', scanId)
        .first() as GdprScanEntity | undefined;

      if (!scanRecord) {
        return null;
      }

      const checkResults = await db('gdpr_check_results')
        .where('scan_id', scanId)
        .orderBy('created_at', 'asc') as GdprCheckResultEntity[];

      return this.mapEntityToResult(scanRecord, checkResults);
    } catch (error) {
      console.error('❌ Failed to retrieve GDPR scan result:', error);
      throw new Error(`Database retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update scan status
   */
  static async updateScanStatus(
    scanId: string,
    status: ScanStatus,
    errorMessage?: string
  ): Promise<void> {
    try {
      await db('gdpr_scans')
        .where('id', scanId)
        .update({
          scan_status: status,
          error_message: errorMessage,
          updated_at: new Date(),
        });
    } catch (error) {
      console.error('❌ Failed to update scan status:', error);
      throw new Error(`Status update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user's GDPR scan history
   */
  static async getUserScans(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<GdprScanEntity[]> {
    try {
      return await db('gdpr_scans')
        .where('user_id', userId)
        .orderBy('scan_timestamp', 'desc')
        .limit(limit)
        .offset(offset) as GdprScanEntity[];
    } catch (error) {
      console.error('❌ Failed to retrieve user scans:', error);
      throw new Error(`Scan history retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Save cookie analysis results
   */
  static async saveCookieAnalysis(
    scanId: string,
    cookies: Array<{
      name: string;
      domain: string;
      category: string;
      hasConsent: boolean;
      secureFlag: boolean;
      httpOnlyFlag: boolean;
      sameSiteAttribute?: string;
      expiryDate?: Date;
      purpose?: string;
      thirdParty: boolean;
    }>
  ): Promise<void> {
    try {
      const cookieRecords = cookies.map((cookie) => ({
        id: uuidv4(),
        scan_id: scanId,
        cookie_name: cookie.name,
        cookie_domain: cookie.domain,
        cookie_category: cookie.category,
        has_consent: cookie.hasConsent,
        secure_flag: cookie.secureFlag,
        httponly_flag: cookie.httpOnlyFlag,
        samesite_attribute: cookie.sameSiteAttribute,
        expiry_date: cookie.expiryDate,
        purpose: cookie.purpose,
        third_party: cookie.thirdParty,
      }));

      await db('gdpr_cookie_analysis').insert(cookieRecords);
    } catch (error) {
      console.error('❌ Failed to save cookie analysis:', error);
      throw new Error(`Cookie analysis save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Save consent analysis results
   */
  static async saveConsentAnalysis(
    scanId: string,
    consentData: {
      consentType: string;
      consentMechanism: string;
      hasRejectOption: boolean;
      hasGranularOptions: boolean;
      preTickedBoxes: boolean;
      consentText: string;
      privacyPolicyLinked: boolean;
      compliant: boolean;
      issues: string[];
    }
  ): Promise<void> {
    try {
      await db('gdpr_consent_analysis').insert({
        id: uuidv4(),
        scan_id: scanId,
        consent_type: consentData.consentType,
        consent_mechanism: consentData.consentMechanism,
        has_reject_option: consentData.hasRejectOption,
        has_granular_options: consentData.hasGranularOptions,
        pre_ticked_boxes: consentData.preTickedBoxes,
        consent_text: consentData.consentText,
        privacy_policy_linked: consentData.privacyPolicyLinked,
        compliant: consentData.compliant,
        issues: consentData.issues,
      });
    } catch (error) {
      console.error('❌ Failed to save consent analysis:', error);
      throw new Error(`Consent analysis save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Save tracker analysis results
   */
  static async saveTrackerAnalysis(
    scanId: string,
    trackers: Array<{
      domain: string;
      type: string;
      name: string;
      loadsBeforeConsent: boolean;
      hasConsentMechanism: boolean;
      dataTransferred: string;
      privacyPolicyMentioned: boolean;
      compliant: boolean;
    }>
  ): Promise<void> {
    try {
      const trackerRecords = trackers.map((tracker) => ({
        id: uuidv4(),
        scan_id: scanId,
        tracker_domain: tracker.domain,
        tracker_type: tracker.type,
        tracker_name: tracker.name,
        loads_before_consent: tracker.loadsBeforeConsent,
        has_consent_mechanism: tracker.hasConsentMechanism,
        data_transferred: tracker.dataTransferred,
        privacy_policy_mentioned: tracker.privacyPolicyMentioned,
        compliant: tracker.compliant,
      }));

      await db('gdpr_tracker_analysis').insert(trackerRecords);
    } catch (error) {
      console.error('❌ Failed to save tracker analysis:', error);
      throw new Error(`Tracker analysis save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Map database entities to result objects
   */
  private static mapEntityToResult(
    scanEntity: GdprScanEntity,
    checkEntities: GdprCheckResultEntity[]
  ): GdprScanResult {
    const checks = checkEntities.map((check) => ({
      ruleId: check.rule_id,
      ruleName: check.rule_name,
      category: check.category,
      passed: check.passed,
      score: check.score,
      weight: check.weight,
      severity: check.severity,
      evidence: Array.isArray(check.evidence) ? check.evidence : [],
      recommendations: Array.isArray(check.recommendations) ? check.recommendations : [],
      manualReviewRequired: check.manual_review_required,
    }));

    const categoryBreakdown = this.calculateCategoryBreakdown(checks);

    return {
      scanId: scanEntity.id,
      targetUrl: scanEntity.target_url,
      timestamp: scanEntity.scan_timestamp.toISOString(),
      scanDuration: scanEntity.scan_duration,
      overallScore: scanEntity.overall_score,
      riskLevel: scanEntity.risk_level,
      status: scanEntity.scan_status,
      summary: {
        totalChecks: scanEntity.total_checks,
        passedChecks: scanEntity.passed_checks,
        failedChecks: scanEntity.failed_checks,
        manualReviewRequired: scanEntity.manual_review_required,
        criticalFailures: checks.filter(c => !c.passed && c.severity === 'critical').length,
        categoryBreakdown,
      },
      checks,
      recommendations: [], // Will be populated by orchestrator
      metadata: scanEntity.metadata as unknown as GdprScanMetadata, // Safe cast for metadata
    };
  }

  /**
   * Calculate category breakdown for summary
   */
  private static calculateCategoryBreakdown(checks: GdprCheckResult[]): CategoryBreakdown[] {
    const categoryMap = new Map<GdprCategory, {
      category: GdprCategory;
      score: number;
      checksInCategory: number;
      passedInCategory: number;
    }>();

    for (const check of checks) {
      const existing = categoryMap.get(check.category) || {
        category: check.category,
        score: 0,
        checksInCategory: 0,
        passedInCategory: 0,
      };

      existing.checksInCategory++;
      if (check.passed) {
        existing.passedInCategory++;
      }
      existing.score = existing.checksInCategory > 0
        ? Math.round((existing.passedInCategory / existing.checksInCategory) * 100)
        : 0;

      categoryMap.set(check.category, existing);
    }

    return Array.from(categoryMap.values());
  }
}
