/**
 * GDPR Compliance Dashboard Page
 * 
 * Main dashboard for GDPR compliance scanning and results management.
 * Follows the same design patterns as HIPAA dashboard for consistency.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  Cookie, 
  Eye, 
  FileText, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  ExternalLink
} from 'lucide-react';
import { GdprScanResult, GdprDashboardData } from '@/types/gdpr';
import GdprApiService from '@/services/gdpr-api';

export default function GdprDashboardPage() {
  const [dashboardData, setDashboardData] = useState<GdprDashboardData | null>(null);
  const [recentScans, setRecentScans] = useState<GdprScanResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load dashboard data and recent scans
      const [dashboard, scans] = await Promise.all([
        GdprApiService.getDashboardData(),
        GdprApiService.getScanHistory(5)
      ]);

      setDashboardData(dashboard);
      setRecentScans(scans);
    } catch (err) {
      console.error('Error loading GDPR dashboard data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getRiskLevelBadgeVariant = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'default';
      default: return 'outline';
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading GDPR dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">GDPR Compliance Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Monitor and manage your GDPR compliance scanning activities
          </p>
        </div>
        <div className="flex gap-3">
          <Button 
            onClick={() => window.location.href = '/dashboard/gdpr/scan'}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Shield className="w-4 h-4 mr-2" />
            New GDPR Scan
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Scans</CardTitle>
            <Shield className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData?.totalScans || 0}</div>
            <p className="text-xs text-gray-600">GDPR compliance scans</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData?.averageScore ? `${dashboardData.averageScore.toFixed(1)}%` : 'N/A'}
            </div>
            <p className="text-xs text-gray-600">Compliance score</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData?.riskDistribution?.critical || 0}
            </div>
            <p className="text-xs text-gray-600">Require immediate attention</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliant Sites</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData?.riskDistribution?.low || 0}
            </div>
            <p className="text-xs text-gray-600">Low risk websites</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Scans */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Recent GDPR Scans
          </CardTitle>
          <CardDescription>
            Your latest GDPR compliance scan results
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentScans.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">No GDPR scans yet</p>
              <Button 
                onClick={() => window.location.href = '/dashboard/gdpr/scan'}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Start Your First GDPR Scan
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {recentScans.map((scan) => (
                <div 
                  key={scan.scanId} 
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className={`w-3 h-3 rounded-full ${getRiskLevelColor(scan.riskLevel)}`} />
                    <div>
                      <p className="font-medium text-gray-900">{scan.targetUrl}</p>
                      <p className="text-sm text-gray-600">
                        {new Date(scan.timestamp).toLocaleDateString()} • 
                        Score: {scan.overallScore}%
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Badge variant={getRiskLevelBadgeVariant(scan.riskLevel) as any}>
                      {scan.riskLevel.toUpperCase()}
                    </Badge>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => window.location.href = `/dashboard/gdpr/results?scanId=${scan.scanId}`}
                    >
                      <ExternalLink className="w-4 h-4 mr-1" />
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => window.location.href = '/dashboard/gdpr/scan'}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cookie className="w-5 h-5 text-blue-600" />
              Cookie Compliance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Analyze cookie usage and consent mechanisms for GDPR compliance
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => window.location.href = '/dashboard/gdpr/scan'}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5 text-purple-600" />
              Privacy Policy Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Review privacy policies for GDPR compliance requirements
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => window.location.href = '/dashboard/gdpr/scan'}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-green-600" />
              Data Rights Check
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Verify implementation of data subject rights and consent mechanisms
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
