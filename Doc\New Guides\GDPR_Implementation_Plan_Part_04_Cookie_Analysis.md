# GDPR Implementation Plan - Part 04: Cookie Analysis and Tracking

## Overview
This document implements cookie classification, tracking detection, and consent monitoring. All implementations analyze real cookies and trackers - NO MOCK DATA.

## ⚠️ CRITICAL REQUIREMENTS
- **Real Cookie Analysis**: Monitor actual cookies from target websites
- **Live Tracker Detection**: Detect real third-party trackers and scripts
- **Actual Consent Testing**: Test real consent mechanisms and cookie blocking
- **No Simulated Data**: All analysis must use genuine website data

## Step 1: Cookie Classification Check (Rule 5)

### 1.1 Create Cookie Classification Service
Create `backend/src/compliance/gdpr/utils/cookie-analyzer.ts`:

```typescript
import { Cookie, CookieAnalysisResult } from '../types';

export class CookieAnalyzer {
  /**
   * Classify cookies by category - REAL cookie analysis
   */
  static classifyCookies(cookies: Cookie[]): {
    essential: Cookie[];
    analytics: Cookie[];
    marketing: Cookie[];
    functional: Cookie[];
    unclassified: <PERSON><PERSON>[];
  } {
    const classification = {
      essential: [] as <PERSON><PERSON>[],
      analytics: [] as <PERSON><PERSON>[],
      marketing: [] as <PERSON><PERSON>[],
      functional: [] as <PERSON><PERSON>[],
      unclassified: [] as <PERSON><PERSON>[],
    };

    for (const cookie of cookies) {
      const category = this.categorizeCookie(cookie);
      classification[category].push(cookie);
    }

    return classification;
  }

  /**
   * Categorize individual cookie based on name and domain
   */
  private static categorizeCookie(cookie: Cookie): 'essential' | 'analytics' | 'marketing' | 'functional' | 'unclassified' {
    const name = cookie.name.toLowerCase();
    const domain = cookie.domain.toLowerCase();

    // Essential cookies (session, security, functionality)
    const essentialPatterns = [
      /^(session|sess|jsessionid|phpsessid|asp\.net_sessionid)/,
      /^(csrf|xsrf|_token)/,
      /^(auth|login|user)/,
      /^(cart|basket|shopping)/,
      /^(lang|language|locale)/,
      /^(currency|region|country)/,
    ];

    if (essentialPatterns.some(pattern => pattern.test(name))) {
      return 'essential';
    }

    // Analytics cookies
    const analyticsPatterns = [
      /^(_ga|_gid|_gat|__utm)/,
      /^(_hjid|_hjIncludedInSample)/,
      /^(adobe|omniture|s_cc|s_sq)/,
      /^(mixpanel|mp_)/,
      /^(amplitude|_amplitude)/,
    ];

    const analyticsDomains = [
      'google-analytics.com',
      'googletagmanager.com',
      'hotjar.com',
      'adobe.com',
      'mixpanel.com',
      'amplitude.com',
    ];

    if (analyticsPatterns.some(pattern => pattern.test(name)) ||
        analyticsDomains.some(d => domain.includes(d))) {
      return 'analytics';
    }

    // Marketing/Advertising cookies
    const marketingPatterns = [
      /^(fb|facebook|_fbp|_fbc)/,
      /^(twitter|twtr)/,
      /^(linkedin|li_)/,
      /^(doubleclick|__gads)/,
      /^(adsystem|adnxs)/,
    ];

    const marketingDomains = [
      'facebook.com',
      'twitter.com',
      'linkedin.com',
      'doubleclick.net',
      'adsystem.amazon.com',
      'adnxs.com',
    ];

    if (marketingPatterns.some(pattern => pattern.test(name)) ||
        marketingDomains.some(d => domain.includes(d))) {
      return 'marketing';
    }

    // Functional cookies
    const functionalPatterns = [
      /^(preferences|prefs|settings)/,
      /^(theme|style|layout)/,
      /^(notification|alert)/,
      /^(chat|support|help)/,
    ];

    if (functionalPatterns.some(pattern => pattern.test(name))) {
      return 'functional';
    }

    return 'unclassified';
  }

  /**
   * Analyze cookie attributes for security compliance
   */
  static analyzeCookieAttributes(cookies: Cookie[]): CookieAnalysisResult[] {
    return cookies.map(cookie => {
      const issues: string[] = [];
      const recommendations: string[] = [];

      // Check Secure flag for HTTPS cookies
      if (!cookie.secure && cookie.domain.startsWith('https')) {
        issues.push('Missing Secure flag for HTTPS cookie');
        recommendations.push('Add Secure flag to prevent transmission over HTTP');
      }

      // Check HttpOnly flag for session cookies
      if (this.isSessionCookie(cookie) && !cookie.httpOnly) {
        issues.push('Missing HttpOnly flag for session cookie');
        recommendations.push('Add HttpOnly flag to prevent XSS attacks');
      }

      // Check SameSite attribute
      if (!cookie.sameSite || cookie.sameSite === 'None') {
        if (cookie.sameSite === 'None' && !cookie.secure) {
          issues.push('SameSite=None requires Secure flag');
          recommendations.push('Add Secure flag when using SameSite=None');
        }
        if (!cookie.sameSite) {
          recommendations.push('Consider adding SameSite attribute for CSRF protection');
        }
      }

      return {
        cookieId: `${cookie.domain}-${cookie.name}`,
        category: this.categorizeCookie(cookie),
        hasConsent: false, // Will be determined by consent analysis
        complianceIssues: issues,
        recommendations,
      };
    });
  }

  /**
   * Check if cookie is likely a session cookie
   */
  private static isSessionCookie(cookie: Cookie): boolean {
    const sessionPatterns = [
      /^(session|sess|jsessionid|phpsessid|asp\.net_sessionid)/i,
      /^(auth|login|user|token)/i,
    ];

    return sessionPatterns.some(pattern => pattern.test(cookie.name)) ||
           !cookie.expires; // Session cookies typically don't have expiry
  }

  /**
   * Monitor cookie changes before and after consent
   */
  static compareCookieStates(
    beforeConsent: Cookie[],
    afterConsent: Cookie[]
  ): {
    newCookies: Cookie[];
    blockedCookies: Cookie[];
    persistentCookies: Cookie[];
  } {
    const beforeMap = new Map(beforeConsent.map(c => [`${c.domain}-${c.name}`, c]));
    const afterMap = new Map(afterConsent.map(c => [`${c.domain}-${c.name}`, c]));

    const newCookies: Cookie[] = [];
    const blockedCookies: Cookie[] = [];
    const persistentCookies: Cookie[] = [];

    // Find new cookies (appeared after consent)
    for (const [key, cookie] of afterMap) {
      if (!beforeMap.has(key)) {
        newCookies.push(cookie);
      } else {
        persistentCookies.push(cookie);
      }
    }

    // Find blocked cookies (disappeared after consent rejection)
    for (const [key, cookie] of beforeMap) {
      if (!afterMap.has(key)) {
        blockedCookies.push(cookie);
      }
    }

    return { newCookies, blockedCookies, persistentCookies };
  }
}
```

### 1.2 Create Cookie Classification Check
Create `backend/src/compliance/gdpr/checks/cookie-classification.ts`:

```typescript
import puppeteer, { Page } from 'puppeteer';
import { GdprCheckResult, Evidence, Cookie } from '../types';
import { CookieAnalyzer } from '../utils/cookie-analyzer';
import { GdprDatabase } from '../database/gdpr-database';

export interface CookieClassificationCheckConfig {
  targetUrl: string;
  timeout: number;
  scanId: string;
}

export class CookieClassificationCheck {
  /**
   * Check cookie classification and consent-based blocking
   * REAL COOKIE MONITORING - analyzes actual website cookies
   */
  async performCheck(config: CookieClassificationCheckConfig): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];
    let passed = false;
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');

      // Step 1: Monitor cookies before consent
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      await page.waitForTimeout(3000); // Allow time for initial cookies

      const cookiesBeforeConsent = await this.extractCookies(page);

      evidence.push({
        type: 'cookie',
        description: 'Cookies found before consent',
        value: `${cookiesBeforeConsent.length} cookies detected`,
      });

      // Step 2: Classify cookies
      const cookieClassification = CookieAnalyzer.classifyCookies(cookiesBeforeConsent);

      // Check if non-essential cookies are present before consent
      const nonEssentialBeforeConsent = [
        ...cookieClassification.analytics,
        ...cookieClassification.marketing,
        ...cookieClassification.functional,
      ];

      if (nonEssentialBeforeConsent.length > 0) {
        evidence.push({
          type: 'cookie',
          description: 'Non-essential cookies loaded before consent',
          value: `${nonEssentialBeforeConsent.length} non-essential cookies found`,
        });
        score -= 30; // Penalty for loading non-essential cookies without consent
      } else {
        evidence.push({
          type: 'cookie',
          description: 'Only essential cookies before consent',
          value: 'Compliant cookie loading',
        });
        score += 40;
      }

      // Step 3: Test consent interaction and monitor cookie changes
      const consentTestResult = await this.testConsentCookieBlocking(page);
      evidence.push(...consentTestResult.evidence);
      score += consentTestResult.score;

      // Step 4: Analyze cookie attributes
      const attributeAnalysis = CookieAnalyzer.analyzeCookieAttributes(cookiesBeforeConsent);
      const attributeScore = this.evaluateCookieAttributes(attributeAnalysis);
      evidence.push(...attributeScore.evidence);
      score += attributeScore.score;

      // Step 5: Save detailed cookie analysis to database
      await this.saveCookieAnalysisToDatabase(config.scanId, cookieClassification, attributeAnalysis);

      // Final scoring
      score = Math.max(0, Math.min(100, score + 30)); // Base score + adjustments
      passed = score >= 70;

      return {
        ruleId: 'GDPR-005',
        ruleName: 'Cookie Classification & Blocking',
        category: 'cookies',
        passed,
        score,
        weight: 8,
        severity: 'critical',
        evidence,
        recommendations: this.generateCookieRecommendations(cookieClassification, score),
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-005',
        ruleName: 'Cookie Classification & Blocking',
        category: 'cookies',
        passed: false,
        score: 0,
        weight: 8,
        severity: 'critical',
        evidence: [{
          type: 'text',
          description: 'Check failed with error',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Fix cookie compliance issues',
          description: 'Implement proper cookie classification and consent-based blocking',
          implementation: 'Review cookie implementation and consent mechanisms',
          effort: 'significant',
          impact: 'high',
        }],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Extract cookies from page - REAL cookie extraction
   */
  private async extractCookies(page: Page): Promise<Cookie[]> {
    const puppeteerCookies = await page.cookies();

    return puppeteerCookies.map(cookie => ({
      name: cookie.name,
      value: cookie.value,
      domain: cookie.domain,
      path: cookie.path,
      secure: cookie.secure,
      httpOnly: cookie.httpOnly,
      sameSite: cookie.sameSite as 'Strict' | 'Lax' | 'None' | undefined,
      expires: cookie.expires ? new Date(cookie.expires * 1000) : undefined,
    }));
  }

  /**
   * Test consent-based cookie blocking - REAL consent interaction
   */
  private async testConsentCookieBlocking(page: Page): Promise<{
    score: number;
    evidence: Evidence[];
  }> {
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      // Look for consent banner
      const bannerExists = await page.evaluate(() => {
        const selectors = [
          '[class*="cookie"]',
          '[class*="consent"]',
          '[id*="cookie"]',
          '[id*="consent"]',
        ];

        for (const selector of selectors) {
          const element = document.querySelector(selector);
          if (element) {
            const rect = element.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0;
          }
        }
        return false;
      });

      if (!bannerExists) {
        evidence.push({
          type: 'element',
          description: 'No consent banner found for cookie testing',
          value: 'Cannot test consent-based blocking',
        });
        return { score: 0, evidence };
      }

      // Try to reject cookies and monitor changes
      const rejectionResult = await page.evaluate(() => {
        const rejectButtons = document.querySelectorAll('button, a, input');
        for (const button of rejectButtons) {
          const text = button.textContent?.toLowerCase() || '';
          if (text.includes('reject') || text.includes('decline') || text.includes('deny')) {
            (button as HTMLElement).click();
            return true;
          }
        }
        return false;
      });

      if (rejectionResult) {
        await page.waitForTimeout(2000); // Wait for cookie changes

        const cookiesAfterRejection = await this.extractCookies(page);

        evidence.push({
          type: 'cookie',
          description: 'Tested cookie rejection',
          value: `${cookiesAfterRejection.length} cookies remain after rejection`,
        });

        // Check if non-essential cookies were properly blocked
        const classification = CookieAnalyzer.classifyCookies(cookiesAfterRejection);
        const nonEssentialAfterRejection = [
          ...classification.analytics,
          ...classification.marketing,
        ];

        if (nonEssentialAfterRejection.length === 0) {
          score += 30;
          evidence.push({
            type: 'cookie',
            description: 'Non-essential cookies properly blocked after rejection',
            value: 'Compliant cookie blocking',
          });
        } else {
          evidence.push({
            type: 'cookie',
            description: 'Non-essential cookies still present after rejection',
            value: `${nonEssentialAfterRejection.length} non-essential cookies not blocked`,
          });
        }
      }

    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Cookie consent testing failed',
        value: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return { score, evidence };
  }

  /**
   * Evaluate cookie attributes compliance
   */
  private evaluateCookieAttributes(analysis: CookieAnalysisResult[]): {
    score: number;
    evidence: Evidence[];
  } {
    const evidence: Evidence[] = [];
    let score = 0;
    let totalCookies = analysis.length;
    let compliantCookies = 0;

    for (const cookieAnalysis of analysis) {
      if (cookieAnalysis.complianceIssues.length === 0) {
        compliantCookies++;
      } else {
        evidence.push({
          type: 'cookie',
          description: `Cookie attribute issues: ${cookieAnalysis.cookieId}`,
          value: cookieAnalysis.complianceIssues.join(', '),
        });
      }
    }

    if (totalCookies > 0) {
      const complianceRate = (compliantCookies / totalCookies) * 100;
      score = Math.round(complianceRate * 0.3); // 30% of total score for attributes

      evidence.push({
        type: 'cookie',
        description: 'Cookie attribute compliance',
        value: `${compliantCookies}/${totalCookies} cookies have proper attributes (${Math.round(complianceRate)}%)`,
      });
    }

    return { score, evidence };
  }

  /**
   * Save detailed cookie analysis to database
   */
  private async saveCookieAnalysisToDatabase(
    scanId: string,
    classification: {
      essential: Cookie[];
      analytics: Cookie[];
      marketing: Cookie[];
      functional: Cookie[];
      unclassified: Cookie[];
    },
    attributeAnalysis: CookieAnalysisResult[]
  ): Promise<void> {
    try {
      const cookieRecords: Array<{
        name: string;
        domain: string;
        category: string;
        hasConsent: boolean;
        secureFlag: boolean;
        httpOnlyFlag: boolean;
        sameSiteAttribute?: string;
        expiryDate?: Date;
        purpose?: string;
        thirdParty: boolean;
      }> = [];

      // Process each category
      for (const [category, cookies] of Object.entries(classification)) {
        for (const cookie of cookies as Cookie[]) {
          const analysis = attributeAnalysis.find(a =>
            a.cookieId === `${cookie.domain}-${cookie.name}`
          );

          cookieRecords.push({
            name: cookie.name,
            domain: cookie.domain,
            category,
            hasConsent: category === 'essential', // Essential cookies don't need explicit consent
            secureFlag: cookie.secure,
            httpOnlyFlag: cookie.httpOnly,
            sameSiteAttribute: cookie.sameSite,
            expiryDate: cookie.expires,
            purpose: this.inferCookiePurpose(cookie, category),
            thirdParty: this.isThirdPartyCookie(cookie),
          });
        }
      }

      await GdprDatabase.saveCookieAnalysis(scanId, cookieRecords);
    } catch (error) {
      console.error('Failed to save cookie analysis:', error);
    }
  }

  /**
   * Infer cookie purpose based on name and category
   */
  private inferCookiePurpose(cookie: Cookie, category: string): string {
    const name = cookie.name.toLowerCase();

    if (category === 'essential') {
      if (name.includes('session') || name.includes('sess')) return 'Session management';
      if (name.includes('csrf') || name.includes('token')) return 'Security token';
      if (name.includes('auth') || name.includes('login')) return 'Authentication';
      if (name.includes('cart') || name.includes('basket')) return 'Shopping cart';
      return 'Essential functionality';
    }

    if (category === 'analytics') {
      if (name.includes('ga') || name.includes('utm')) return 'Google Analytics';
      if (name.includes('hj')) return 'Hotjar analytics';
      return 'Website analytics';
    }

    if (category === 'marketing') {
      if (name.includes('fb') || name.includes('facebook')) return 'Facebook advertising';
      if (name.includes('gads') || name.includes('doubleclick')) return 'Google advertising';
      return 'Marketing and advertising';
    }

    return 'Functional enhancement';
  }

  /**
   * Check if cookie is from third party
   */
  private isThirdPartyCookie(cookie: Cookie): boolean {
    const thirdPartyDomains = [
      'google.com', 'facebook.com', 'twitter.com', 'linkedin.com',
      'doubleclick.net', 'adsystem.amazon.com', 'hotjar.com',
    ];

    return thirdPartyDomains.some(domain => cookie.domain.includes(domain));
  }

  /**
   * Generate cookie-specific recommendations
   */
  private generateCookieRecommendations(classification: {
    analytics: Cookie[];
    marketing: Cookie[];
    functional: Cookie[];
    unclassified: Cookie[];
  }, score: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    const nonEssentialCount = classification.analytics.length +
                             classification.marketing.length +
                             classification.functional.length;

    if (nonEssentialCount > 0 && score < 70) {
      recommendations.push({
        priority: 1,
        title: 'Implement consent-based cookie blocking',
        description: 'Block non-essential cookies until user provides consent',
        implementation: 'Configure cookie management system to block analytics and marketing cookies before consent',
        effort: 'significant',
        impact: 'high',
      });
    }

    if (classification.unclassified.length > 0) {
      recommendations.push({
        priority: 2,
        title: 'Classify unidentified cookies',
        description: `${classification.unclassified.length} cookies need proper classification`,
        implementation: 'Review and categorize all cookies according to their purpose',
        effort: 'moderate',
        impact: 'medium',
      });
    }

    return recommendations;
  }
}
```

## Step 2: Third-Party Tracker Detection (Rule 6)

### 2.1 Create Tracker Detection Service
Create `backend/src/compliance/gdpr/utils/tracker-database.ts`:

```typescript
// Known tracker domains and their classifications
export const TRACKER_DATABASE = {
  // Analytics Trackers
  analytics: [
    { domain: 'google-analytics.com', name: 'Google Analytics', dataTypes: ['pageviews', 'user_behavior', 'demographics'] },
    { domain: 'googletagmanager.com', name: 'Google Tag Manager', dataTypes: ['tag_management', 'event_tracking'] },
    { domain: 'hotjar.com', name: 'Hotjar', dataTypes: ['heatmaps', 'session_recordings', 'user_feedback'] },
    { domain: 'adobe.com', name: 'Adobe Analytics', dataTypes: ['web_analytics', 'customer_journey'] },
    { domain: 'mixpanel.com', name: 'Mixpanel', dataTypes: ['event_analytics', 'user_behavior'] },
    { domain: 'amplitude.com', name: 'Amplitude', dataTypes: ['product_analytics', 'user_retention'] },
  ],

  // Advertising Trackers
  advertising: [
    { domain: 'doubleclick.net', name: 'Google DoubleClick', dataTypes: ['ad_targeting', 'conversion_tracking'] },
    { domain: 'facebook.com', name: 'Facebook Pixel', dataTypes: ['ad_targeting', 'conversion_tracking', 'audience_building'] },
    { domain: 'twitter.com', name: 'Twitter Analytics', dataTypes: ['ad_performance', 'audience_insights'] },
    { domain: 'linkedin.com', name: 'LinkedIn Insight Tag', dataTypes: ['b2b_targeting', 'conversion_tracking'] },
    { domain: 'adsystem.amazon.com', name: 'Amazon DSP', dataTypes: ['product_advertising', 'audience_targeting'] },
    { domain: 'adnxs.com', name: 'AppNexus', dataTypes: ['programmatic_advertising', 'real_time_bidding'] },
  ],

  // Social Media Trackers
  social: [
    { domain: 'youtube.com', name: 'YouTube', dataTypes: ['video_analytics', 'user_engagement'] },
    { domain: 'instagram.com', name: 'Instagram', dataTypes: ['social_engagement', 'content_performance'] },
    { domain: 'tiktok.com', name: 'TikTok Pixel', dataTypes: ['video_engagement', 'ad_targeting'] },
    { domain: 'pinterest.com', name: 'Pinterest Tag', dataTypes: ['content_discovery', 'shopping_behavior'] },
  ],

  // Functional Trackers
  functional: [
    { domain: 'zendesk.com', name: 'Zendesk', dataTypes: ['customer_support', 'chat_interactions'] },
    { domain: 'intercom.io', name: 'Intercom', dataTypes: ['customer_messaging', 'user_onboarding'] },
    { domain: 'drift.com', name: 'Drift', dataTypes: ['conversational_marketing', 'lead_qualification'] },
    { domain: 'hubspot.com', name: 'HubSpot', dataTypes: ['crm_integration', 'marketing_automation'] },
  ],
} as const;

export class TrackerDatabase {
  /**
   * Classify tracker by domain
   */
  static classifyTracker(domain: string): {
    category: 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';
    name: string;
    dataTypes: string[];
    riskLevel: 'critical' | 'high' | 'medium' | 'low';
  } {
    // Check each category
    for (const [category, trackers] of Object.entries(TRACKER_DATABASE)) {
      for (const tracker of trackers) {
        if (domain.includes(tracker.domain) || tracker.domain.includes(domain)) {
          return {
            category: category as any,
            name: tracker.name,
            dataTypes: tracker.dataTypes,
            riskLevel: this.determineRiskLevel(category as any, tracker.dataTypes),
          };
        }
      }
    }

    return {
      category: 'unknown',
      name: 'Unknown Tracker',
      dataTypes: ['unknown'],
      riskLevel: 'medium',
    };
  }

  /**
   * Determine risk level based on tracker type and data collection
   */
  private static determineRiskLevel(
    category: string,
    dataTypes: string[]
  ): 'critical' | 'high' | 'medium' | 'low' {
    // High-risk data types
    const highRiskTypes = ['conversion_tracking', 'audience_building', 'ad_targeting', 'session_recordings'];

    if (category === 'advertising' && dataTypes.some(type => highRiskTypes.includes(type))) {
      return 'critical';
    }

    if (category === 'advertising' || dataTypes.some(type => highRiskTypes.includes(type))) {
      return 'high';
    }

    if (category === 'analytics' || category === 'social') {
      return 'medium';
    }

    return 'low';
  }

  /**
   * Get all known tracker domains
   */
  static getAllTrackerDomains(): string[] {
    const domains: string[] = [];

    for (const trackers of Object.values(TRACKER_DATABASE)) {
      for (const tracker of trackers) {
        domains.push(tracker.domain);
      }
    }

    return domains;
  }
}
```

## Step 3: Complete Tracker Detection Check Implementation

### 3.1 Create Tracker Detection Check
Create `backend/src/compliance/gdpr/checks/tracker-detection.ts`:

```typescript
import puppeteer, { Page } from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';
import { TrackerDatabase } from '../utils/tracker-database';
import { GdprDatabase } from '../database/gdpr-database';

export interface TrackerDetectionCheckConfig {
  targetUrl: string;
  timeout: number;
  scanId?: string;
}

export class TrackerDetectionCheck {
  /**
   * Detect third-party trackers and analyze consent compliance
   * REAL ANALYSIS - monitors actual network requests and scripts
   */
  async performCheck(config: TrackerDetectionCheckConfig): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');

      // Monitor network requests
      const networkRequests: Array<{
        url: string;
        domain: string;
        type: string;
        beforeConsent: boolean;
      }> = [];

      page.on('request', (request) => {
        const url = request.url();
        const domain = new URL(url).hostname;
        networkRequests.push({
          url,
          domain,
          type: request.resourceType(),
          beforeConsent: true, // Initially all requests are before consent
        });
      });

      // Navigate to website
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      await page.waitForTimeout(3000); // Allow time for trackers to load

      // Analyze trackers found before consent
      const trackersBeforeConsent = this.analyzeTrackers(networkRequests);

      // Test consent interaction if banner exists
      const consentTestResult = await this.testConsentTrackerBlocking(page);

      // Calculate compliance score
      const totalTrackers = trackersBeforeConsent.length;
      const highRiskTrackers = trackersBeforeConsent.filter(t =>
        t.classification.riskLevel === 'critical' || t.classification.riskLevel === 'high'
      ).length;

      if (totalTrackers === 0) {
        score = 100;
        evidence.push({
          type: 'network',
          description: 'No third-party trackers detected',
          value: 'No tracking concerns',
        });
      } else {
        // Penalty for high-risk trackers loading before consent
        const riskPenalty = highRiskTrackers * 20;
        score = Math.max(0, 100 - riskPenalty);

        evidence.push({
          type: 'network',
          description: 'Third-party trackers detected',
          value: `${totalTrackers} trackers found, ${highRiskTrackers} high-risk`,
        });

        // Add evidence for each tracker
        for (const tracker of trackersBeforeConsent.slice(0, 5)) { // Limit to top 5
          evidence.push({
            type: 'network',
            description: `Tracker: ${tracker.classification.name}`,
            value: `Domain: ${tracker.domain}, Risk: ${tracker.classification.riskLevel}`,
          });
        }
      }

      // Apply consent test results
      if (consentTestResult.tested) {
        if (consentTestResult.blockingWorking) {
          score += 20; // Bonus for working consent blocking
          evidence.push({
            type: 'network',
            description: 'Consent-based tracker blocking working',
            value: consentTestResult.evidence,
          });
        } else {
          evidence.push({
            type: 'network',
            description: 'Consent-based tracker blocking not working',
            value: consentTestResult.evidence,
          });
        }
      }

      // Save tracker analysis to database
      if (config.scanId) {
        await this.saveTrackerAnalysisToDatabase(config.scanId, trackersBeforeConsent);
      }

      const passed = score >= 70;

      return {
        ruleId: 'GDPR-006',
        ruleName: 'Third-Party Tracker Detection',
        category: 'cookies',
        passed,
        score: Math.min(100, score),
        weight: 6,
        severity: 'medium',
        evidence,
        recommendations: this.generateTrackerRecommendations(trackersBeforeConsent, score),
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-006',
        ruleName: 'Third-Party Tracker Detection',
        category: 'cookies',
        passed: false,
        score: 0,
        weight: 6,
        severity: 'medium',
        evidence: [{
          type: 'text',
          description: 'Tracker detection failed',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Implement tracker consent management',
          description: 'Add consent-based blocking for third-party trackers',
          implementation: 'Configure consent management platform to block trackers',
          effort: 'significant',
          impact: 'high',
        }],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze network requests for trackers
   */
  private analyzeTrackers(requests: Array<{
    url: string;
    domain: string;
    type: string;
    beforeConsent: boolean;
  }>): Array<{
    domain: string;
    url: string;
    classification: {
      category: 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';
      name: string;
      dataTypes: string[];
      riskLevel: 'critical' | 'high' | 'medium' | 'low';
    };
    beforeConsent: boolean;
  }> {
    const trackers: Array<{
      domain: string;
      url: string;
      classification: {
        category: 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';
        name: string;
        dataTypes: string[];
        riskLevel: 'critical' | 'high' | 'medium' | 'low';
      };
      beforeConsent: boolean;
    }> = [];

    const uniqueDomains = new Set<string>();

    for (const request of requests) {
      if (!uniqueDomains.has(request.domain)) {
        uniqueDomains.add(request.domain);

        const classification = TrackerDatabase.classifyTracker(request.domain);

        // Only include known trackers (not unknown)
        if (classification.category !== 'unknown') {
          trackers.push({
            domain: request.domain,
            url: request.url,
            classification,
            beforeConsent: request.beforeConsent,
          });
        }
      }
    }

    return trackers;
  }

  /**
   * Test consent-based tracker blocking
   */
  private async testConsentTrackerBlocking(page: Page): Promise<{
    tested: boolean;
    blockingWorking: boolean;
    evidence: string;
  }> {
    try {
      // Look for consent banner
      const bannerExists = await page.evaluate(() => {
        const selectors = [
          '[class*="cookie"]',
          '[class*="consent"]',
          '[id*="cookie"]',
          '[id*="consent"]',
        ];

        for (const selector of selectors) {
          const element = document.querySelector(selector);
          if (element) {
            const rect = element.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0;
          }
        }
        return false;
      });

      if (!bannerExists) {
        return {
          tested: false,
          blockingWorking: false,
          evidence: 'No consent banner found for testing',
        };
      }

      // Try to reject cookies/trackers
      const rejectionResult = await page.evaluate(() => {
        const rejectButtons = document.querySelectorAll('button, a, input');
        for (const button of rejectButtons) {
          const text = button.textContent?.toLowerCase() || '';
          if (text.includes('reject') || text.includes('decline') || text.includes('deny')) {
            (button as HTMLElement).click();
            return true;
          }
        }
        return false;
      });

      if (rejectionResult) {
        await page.waitForTimeout(2000); // Wait for changes

        // Check if tracking scripts are still active
        const trackingStillActive = await page.evaluate(() => {
          // Check for common tracking functions
          const trackingIndicators = [
            typeof (window as any).gtag === 'function',
            typeof (window as any).ga === 'function',
            typeof (window as any).fbq === 'function',
            document.cookie.includes('_ga'),
            document.cookie.includes('_fbp'),
          ];

          return trackingIndicators.some(indicator => indicator);
        });

        return {
          tested: true,
          blockingWorking: !trackingStillActive,
          evidence: trackingStillActive
            ? 'Tracking still active after rejection'
            : 'Tracking properly blocked after rejection',
        };
      }

      return {
        tested: false,
        blockingWorking: false,
        evidence: 'Could not interact with consent controls',
      };

    } catch (error) {
      return {
        tested: false,
        blockingWorking: false,
        evidence: `Consent testing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Save tracker analysis to database
   */
  private async saveTrackerAnalysisToDatabase(
    scanId: string,
    trackers: Array<{
      domain: string;
      classification: {
        category: 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';
        name: string;
        dataTypes: string[];
        riskLevel: 'critical' | 'high' | 'medium' | 'low';
      };
      beforeConsent: boolean;
    }>
  ): Promise<void> {
    try {
      const trackerRecords = trackers.map(tracker => ({
        domain: tracker.domain,
        name: tracker.classification.name,
        type: tracker.classification.category,
        loadsBeforeConsent: tracker.beforeConsent,
        hasConsentMechanism: false, // Would need deeper analysis
        dataTransferred: tracker.classification.dataTypes.join(', '),
        privacyPolicyMentioned: false, // Would need policy analysis
        compliant: !tracker.beforeConsent || tracker.classification.riskLevel === 'low',
      }));

      await GdprDatabase.saveTrackerAnalysis(scanId, trackerRecords);
    } catch (error) {
      console.error('Failed to save tracker analysis:', error);
    }
  }

  /**
   * Generate tracker-specific recommendations
   */
  private generateTrackerRecommendations(trackers: Array<{
    classification: {
      riskLevel: 'critical' | 'high' | 'medium' | 'low';
    };
  }>, score: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    const highRiskTrackers = trackers.filter(t =>
      t.classification.riskLevel === 'critical' || t.classification.riskLevel === 'high'
    );

    if (highRiskTrackers.length > 0) {
      recommendations.push({
        priority: 1,
        title: 'Implement consent for high-risk trackers',
        description: `${highRiskTrackers.length} high-risk trackers need consent management`,
        implementation: 'Configure consent management to block high-risk trackers before consent',
        effort: 'significant',
        impact: 'high',
      });
    }

    if (trackers.length > 0 && score < 70) {
      recommendations.push({
        priority: 2,
        title: 'Review all third-party integrations',
        description: 'Audit all tracking and analytics integrations for GDPR compliance',
        implementation: 'Document all trackers and implement proper consent mechanisms',
        effort: 'moderate',
        impact: 'high',
      });
    }

    return recommendations;
  }
}
```

## Next Steps
Continue with Part 05: API Integration and Frontend Components to complete the GDPR implementation.

## Validation Checklist
- [ ] Cookie analysis uses real website cookies (no mock data)
- [ ] Tracker detection monitors actual network requests
- [ ] Cookie classification based on real cookie attributes
- [ ] Consent testing interacts with real consent banners
- [ ] Database storage of actual cookie and tracker data
- [ ] TypeScript strict compliance (no `any` types)
- [ ] Ready for API and frontend integration