# GDPR Implementation Plan - Part 03: Individual Check Implementations

## Overview
This document implements the individual GDPR compliance checks. Each check performs real website analysis - NO MOCK DATA allowed.

## ⚠️ CRITICAL REQUIREMENTS
- **Real Website Analysis**: All checks must analyze actual websites using live HTTP requests
- **No Simulated Data**: Prohibited - use real content, cookies, and network traffic
- **TypeScript Strict Mode**: NO `any` types - follow .projectrules strictly
- **Comprehensive Evidence**: Collect real evidence from actual website scanning

## Step 1: HTTPS/TLS Check (Rule 1)

### 1.1 Create HTTPS/TLS Check
Create `backend/src/compliance/gdpr/checks/https-tls.ts`:

```typescript
import https from 'https';
import { URL } from 'url';
import { GdprCheckResult, Evidence } from '../types';

export interface HttpsTlsCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class HttpsTlsCheck {
  /**
   * Check HTTPS/TLS encryption compliance
   * REAL ANALYSIS - connects to actual website
   */
  async performCheck(config: HttpsTlsCheckConfig): Promise<GdprCheckResult> {
    const evidence: Evidence[] = [];
    let passed = true;
    const issues: string[] = [];

    try {
      const url = new URL(config.targetUrl);
      
      // Check if URL uses HTTPS
      if (url.protocol !== 'https:') {
        passed = false;
        issues.push('Website does not use HTTPS encryption');
        evidence.push({
          type: 'text',
          description: 'Protocol check failed',
          value: url.protocol,
        });
      }

      // Perform actual TLS certificate analysis
      const tlsInfo = await this.analyzeTlsCertificate(url.hostname, url.port || '443');
      
      if (!tlsInfo.valid) {
        passed = false;
        issues.push('Invalid or expired TLS certificate');
        evidence.push({
          type: 'text',
          description: 'TLS certificate validation failed',
          value: tlsInfo.error || 'Certificate invalid',
        });
      }

      // Check for HSTS header
      const hstsCheck = await this.checkHstsHeader(config.targetUrl);
      if (!hstsCheck.present) {
        passed = false;
        issues.push('Missing Strict-Transport-Security header');
        evidence.push({
          type: 'network',
          description: 'HSTS header check',
          value: 'Header not found',
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'HSTS header found',
          value: hstsCheck.value || 'Present',
        });
      }

      return {
        ruleId: 'GDPR-001',
        ruleName: 'HTTPS/TLS Encryption',
        category: 'security',
        passed,
        score: passed ? 100 : 0,
        weight: 8,
        severity: 'critical',
        evidence,
        recommendations: passed ? [] : [
          {
            priority: 1,
            title: 'Implement HTTPS encryption',
            description: 'Enable HTTPS with valid TLS certificate and HSTS header',
            implementation: 'Configure web server with TLS certificate and security headers',
            effort: 'moderate',
            impact: 'high',
          },
        ],
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-001',
        ruleName: 'HTTPS/TLS Encryption',
        category: 'security',
        passed: false,
        score: 0,
        weight: 8,
        severity: 'critical',
        evidence: [{
          type: 'text',
          description: 'Check failed with error',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Fix HTTPS configuration',
          description: 'Resolve HTTPS/TLS configuration issues',
          implementation: 'Check server configuration and certificate validity',
          effort: 'moderate',
          impact: 'high',
        }],
        manualReviewRequired: false,
      };
    }
  }

  /**
   * Analyze TLS certificate - REAL certificate validation
   */
  private async analyzeTlsCertificate(hostname: string, port: string): Promise<{
    valid: boolean;
    error?: string;
    certificate?: Record<string, unknown>;
  }> {
    return new Promise((resolve) => {
      const options = {
        hostname,
        port: parseInt(port),
        method: 'GET',
        rejectUnauthorized: false, // We want to check the cert ourselves
      };

      const req = https.request(options, (res) => {
        const cert = res.socket.getPeerCertificate();
        
        if (!cert || Object.keys(cert).length === 0) {
          resolve({ valid: false, error: 'No certificate found' });
          return;
        }

        // Check certificate validity
        const now = new Date();
        const validFrom = new Date(cert.valid_from);
        const validTo = new Date(cert.valid_to);

        if (now < validFrom || now > validTo) {
          resolve({ 
            valid: false, 
            error: `Certificate expired or not yet valid. Valid from ${validFrom} to ${validTo}` 
          });
          return;
        }

        resolve({ valid: true, certificate: cert });
      });

      req.on('error', (error) => {
        resolve({ valid: false, error: error.message });
      });

      req.setTimeout(10000, () => {
        req.destroy();
        resolve({ valid: false, error: 'Connection timeout' });
      });

      req.end();
    });
  }

  /**
   * Check for HSTS header - REAL HTTP request
   */
  private async checkHstsHeader(url: string): Promise<{
    present: boolean;
    value?: string;
  }> {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        redirect: 'follow',
      });

      const hstsHeader = response.headers.get('strict-transport-security');
      
      return {
        present: !!hstsHeader,
        value: hstsHeader || undefined,
      };
    } catch (error) {
      return { present: false };
    }
  }
}
```

## Step 2: Privacy Policy Presence Check (Rule 2)

### 2.1 Create Privacy Policy Check
Create `backend/src/compliance/gdpr/checks/privacy-policy.ts`:

```typescript
import puppeteer, { Page } from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';

export interface PrivacyPolicyCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class PrivacyPolicyCheck {
  /**
   * Check for privacy policy presence and accessibility
   * REAL WEBSITE ANALYSIS - scans actual webpage content
   */
  async performCheck(config: PrivacyPolicyCheckConfig): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];
    let passed = false;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      // Navigate to actual website
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Search for privacy policy links - REAL DOM analysis
      const privacyLinks = await this.findPrivacyPolicyLinks(page);
      
      if (privacyLinks.length === 0) {
        evidence.push({
          type: 'element',
          description: 'No privacy policy links found',
          location: 'Page scan',
        });
      } else {
        passed = true;
        
        for (const link of privacyLinks) {
          evidence.push({
            type: 'element',
            description: 'Privacy policy link found',
            location: link.location,
            value: link.text,
          });
        }

        // Verify links are accessible - REAL link validation
        const linkValidation = await this.validatePrivacyPolicyLinks(page, privacyLinks);
        
        if (linkValidation.accessibleLinks === 0) {
          passed = false;
          evidence.push({
            type: 'network',
            description: 'Privacy policy links are not accessible',
            value: `${linkValidation.brokenLinks} broken links found`,
          });
        }
      }

      return {
        ruleId: 'GDPR-002',
        ruleName: 'Privacy Policy Presence',
        category: 'privacy_policy',
        passed,
        score: passed ? 100 : 0,
        weight: 7,
        severity: 'high',
        evidence,
        recommendations: passed ? [] : [
          {
            priority: 1,
            title: 'Add privacy policy link',
            description: 'Include a clearly labeled privacy policy link in the website footer or header',
            implementation: 'Add link with text "Privacy Policy" or "Privacy Notice" that leads to comprehensive privacy information',
            effort: 'minimal',
            impact: 'high',
          },
        ],
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-002',
        ruleName: 'Privacy Policy Presence',
        category: 'privacy_policy',
        passed: false,
        score: 0,
        weight: 7,
        severity: 'high',
        evidence: [{
          type: 'text',
          description: 'Check failed with error',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Fix website accessibility',
          description: 'Ensure website is accessible for privacy policy scanning',
          implementation: 'Check website loading and accessibility issues',
          effort: 'moderate',
          impact: 'medium',
        }],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Find privacy policy links on the page - REAL DOM scanning
   */
  private async findPrivacyPolicyLinks(page: Page): Promise<Array<{
    text: string;
    href: string;
    location: string;
  }>> {
    return await page.evaluate(() => {
      const links: Array<{ text: string; href: string; location: string }> = [];
      
      // Search for privacy policy related links
      const privacyPatterns = [
        /privacy\s*policy/i,
        /privacy\s*notice/i,
        /privacy\s*statement/i,
        /data\s*protection/i,
        /gdpr/i,
      ];

      const allLinks = document.querySelectorAll('a[href]');
      
      allLinks.forEach((link, index) => {
        const text = link.textContent?.trim() || '';
        const href = (link as HTMLAnchorElement).href;
        
        // Check if link text matches privacy patterns
        const matchesPattern = privacyPatterns.some(pattern => pattern.test(text));
        
        // Also check href for privacy-related paths
        const hrefMatchesPattern = privacyPatterns.some(pattern => pattern.test(href));
        
        if (matchesPattern || hrefMatchesPattern) {
          // Determine location context
          let location = 'Unknown';
          const parent = link.closest('footer, header, nav, .footer, .header, .navigation');
          if (parent) {
            location = parent.tagName.toLowerCase();
          }
          
          links.push({
            text,
            href,
            location: `${location} (link ${index + 1})`,
          });
        }
      });

      return links;
    });
  }

  /**
   * Validate privacy policy links accessibility - REAL link testing
   */
  private async validatePrivacyPolicyLinks(
    page: Page, 
    links: Array<{ href: string; text: string }>
  ): Promise<{
    accessibleLinks: number;
    brokenLinks: number;
  }> {
    let accessibleLinks = 0;
    let brokenLinks = 0;

    for (const link of links) {
      try {
        // Test if link is accessible
        const response = await page.goto(link.href, {
          waitUntil: 'networkidle2',
          timeout: 10000,
        });

        if (response && response.status() < 400) {
          accessibleLinks++;
        } else {
          brokenLinks++;
        }
      } catch (error) {
        brokenLinks++;
      }
    }

    return { accessibleLinks, brokenLinks };
  }
}
```

## Step 3: Cookie Consent Banner Check (Rule 4)

### 3.1 Create Cookie Consent Check
Create `backend/src/compliance/gdpr/checks/cookie-consent.ts`:

```typescript
import puppeteer, { Page } from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';

export interface CookieConsentCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class CookieConsentCheck {
  /**
   * Check cookie consent banner compliance
   * REAL WEBSITE INTERACTION - tests actual consent mechanisms
   */
  async performCheck(config: CookieConsentCheckConfig): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];
    let passed = false;
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      // Clear cookies to ensure fresh consent state
      await page.deleteCookie(...(await page.cookies()));
      
      // Navigate to actual website
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Wait for potential consent banner to appear
      await page.waitForTimeout(3000);

      // Analyze consent banner - REAL DOM analysis
      const bannerAnalysis = await this.analyzeConsentBanner(page);
      
      if (!bannerAnalysis.bannerFound) {
        evidence.push({
          type: 'element',
          description: 'No cookie consent banner detected',
          location: 'Page scan',
        });
        score = 0;
      } else {
        evidence.push({
          type: 'element',
          description: 'Cookie consent banner found',
          location: bannerAnalysis.location || 'Unknown',
          value: bannerAnalysis.bannerText,
        });

        // Evaluate banner compliance
        const complianceScore = this.evaluateBannerCompliance(bannerAnalysis);
        score = complianceScore.score;
        passed = complianceScore.score >= 70; // 70% threshold for passing

        // Add detailed evidence
        evidence.push(...complianceScore.evidence);

        // Test consent functionality - REAL interaction testing
        const functionalityTest = await this.testConsentFunctionality(page, bannerAnalysis);
        evidence.push(...functionalityTest.evidence);
        
        if (!functionalityTest.working) {
          score = Math.max(0, score - 30); // Penalty for non-functional consent
          passed = false;
        }
      }

      return {
        ruleId: 'GDPR-004',
        ruleName: 'Cookie Consent Banner',
        category: 'consent',
        passed,
        score,
        weight: 9,
        severity: 'critical',
        evidence,
        recommendations: this.generateRecommendations(bannerAnalysis, score),
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-004',
        ruleName: 'Cookie Consent Banner',
        category: 'consent',
        passed: false,
        score: 0,
        weight: 9,
        severity: 'critical',
        evidence: [{
          type: 'text',
          description: 'Check failed with error',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Implement cookie consent banner',
          description: 'Add GDPR-compliant cookie consent mechanism',
          implementation: 'Install cookie consent solution with accept/reject options',
          effort: 'moderate',
          impact: 'high',
        }],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze consent banner on the page - REAL DOM analysis
   */
  private async analyzeConsentBanner(page: Page): Promise<{
    bannerFound: boolean;
    bannerText?: string;
    hasAcceptButton: boolean;
    hasRejectButton: boolean;
    hasGranularOptions: boolean;
    hasPrivacyPolicyLink: boolean;
    location?: string;
    preTickedBoxes: boolean;
  }> {
    return await page.evaluate(() => {
      // Common selectors for cookie banners
      const bannerSelectors = [
        '[class*="cookie"]',
        '[class*="consent"]',
        '[class*="gdpr"]',
        '[id*="cookie"]',
        '[id*="consent"]',
        '[data-testid*="cookie"]',
        '.cookie-banner',
        '.consent-banner',
        '#cookie-notice',
        '#consent-notice',
      ];

      let bannerElement: Element | null = null;
      
      // Find banner element
      for (const selector of bannerSelectors) {
        const elements = document.querySelectorAll(selector);
        for (const element of elements) {
          // Check if element is visible and contains consent-related text
          const rect = element.getBoundingClientRect();
          const text = element.textContent?.toLowerCase() || '';
          
          if (rect.width > 0 && rect.height > 0 && 
              (text.includes('cookie') || text.includes('consent') || text.includes('privacy'))) {
            bannerElement = element;
            break;
          }
        }
        if (bannerElement) break;
      }

      if (!bannerElement) {
        return {
          bannerFound: false,
          hasAcceptButton: false,
          hasRejectButton: false,
          hasGranularOptions: false,
          hasPrivacyPolicyLink: false,
          preTickedBoxes: false,
        };
      }

      const bannerText = bannerElement.textContent?.trim() || '';
      
      // Check for buttons
      const buttons = bannerElement.querySelectorAll('button, a, input[type="button"], input[type="submit"]');
      let hasAcceptButton = false;
      let hasRejectButton = false;
      
      buttons.forEach(button => {
        const buttonText = button.textContent?.toLowerCase() || '';
        const buttonValue = (button as HTMLInputElement).value?.toLowerCase() || '';
        const combinedText = buttonText + ' ' + buttonValue;
        
        if (combinedText.includes('accept') || combinedText.includes('agree') || combinedText.includes('allow')) {
          hasAcceptButton = true;
        }
        if (combinedText.includes('reject') || combinedText.includes('decline') || combinedText.includes('deny')) {
          hasRejectButton = true;
        }
      });

      // Check for granular options (checkboxes, toggles)
      const checkboxes = bannerElement.querySelectorAll('input[type="checkbox"], input[type="radio"], [role="switch"]');
      const hasGranularOptions = checkboxes.length > 1; // More than just accept/reject

      // Check for privacy policy links
      const links = bannerElement.querySelectorAll('a[href]');
      let hasPrivacyPolicyLink = false;
      links.forEach(link => {
        const linkText = link.textContent?.toLowerCase() || '';
        const href = (link as HTMLAnchorElement).href.toLowerCase();
        if (linkText.includes('privacy') || linkText.includes('policy') || 
            href.includes('privacy') || href.includes('policy')) {
          hasPrivacyPolicyLink = true;
        }
      });

      // Check for pre-ticked boxes
      const preTickedBoxes = Array.from(checkboxes).some(checkbox => 
        (checkbox as HTMLInputElement).checked
      );

      return {
        bannerFound: true,
        bannerText,
        hasAcceptButton,
        hasRejectButton,
        hasGranularOptions,
        hasPrivacyPolicyLink,
        location: bannerElement.tagName.toLowerCase(),
        preTickedBoxes,
      };
    });
  }

  /**
   * Evaluate banner compliance and generate score
   */
  private evaluateBannerCompliance(analysis: {
    hasAcceptButton: boolean;
    hasRejectButton: boolean;
    hasPrivacyPolicyLink: boolean;
    hasGranularOptions: boolean;
    preTickedBoxes: boolean;
  }): {
    score: number;
    evidence: Evidence[];
  } {
    const evidence: Evidence[] = [];
    let score = 0;

    // Accept button (required) - 25 points
    if (analysis.hasAcceptButton) {
      score += 25;
      evidence.push({
        type: 'element',
        description: 'Accept button found',
        value: 'Compliant',
      });
    } else {
      evidence.push({
        type: 'element',
        description: 'Accept button missing',
        value: 'Non-compliant',
      });
    }

    // Reject button (required) - 25 points
    if (analysis.hasRejectButton) {
      score += 25;
      evidence.push({
        type: 'element',
        description: 'Reject button found',
        value: 'Compliant',
      });
    } else {
      evidence.push({
        type: 'element',
        description: 'Reject button missing',
        value: 'Non-compliant',
      });
    }

    // Privacy policy link (recommended) - 20 points
    if (analysis.hasPrivacyPolicyLink) {
      score += 20;
      evidence.push({
        type: 'element',
        description: 'Privacy policy link found',
        value: 'Compliant',
      });
    } else {
      evidence.push({
        type: 'element',
        description: 'Privacy policy link missing',
        value: 'Improvement needed',
      });
    }

    // Granular options (recommended) - 20 points
    if (analysis.hasGranularOptions) {
      score += 20;
      evidence.push({
        type: 'element',
        description: 'Granular consent options found',
        value: 'Compliant',
      });
    } else {
      evidence.push({
        type: 'element',
        description: 'Granular consent options missing',
        value: 'Improvement recommended',
      });
    }

    // No pre-ticked boxes (required) - 10 points
    if (!analysis.preTickedBoxes) {
      score += 10;
      evidence.push({
        type: 'element',
        description: 'No pre-ticked consent boxes',
        value: 'Compliant',
      });
    } else {
      evidence.push({
        type: 'element',
        description: 'Pre-ticked consent boxes found',
        value: 'Non-compliant - violates GDPR',
      });
    }

    return { score, evidence };
  }

  /**
   * Test consent functionality - REAL interaction testing
   */
  private async testConsentFunctionality(page: Page, analysis: {
    hasAcceptButton: boolean;
    hasRejectButton: boolean;
  }): Promise<{
    working: boolean;
    evidence: Evidence[];
  }> {
    const evidence: Evidence[] = [];
    let working = true;

    try {
      // Try to interact with accept button if present
      if (analysis.hasAcceptButton) {
        const acceptClicked = await page.evaluate(() => {
          const buttons = document.querySelectorAll('button, a, input[type="button"]');
          for (const button of buttons) {
            const text = button.textContent?.toLowerCase() || '';
            if (text.includes('accept') || text.includes('agree')) {
              (button as HTMLElement).click();
              return true;
            }
          }
          return false;
        });

        if (acceptClicked) {
          evidence.push({
            type: 'element',
            description: 'Accept button functional',
            value: 'Successfully clicked',
          });
        } else {
          working = false;
          evidence.push({
            type: 'element',
            description: 'Accept button not functional',
            value: 'Click failed',
          });
        }
      }

    } catch (error) {
      working = false;
      evidence.push({
        type: 'element',
        description: 'Consent functionality test failed',
        value: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    return { working, evidence };
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(analysis: {
    hasRejectButton: boolean;
    hasAcceptButton: boolean;
  }, score: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (score < 70) {
      recommendations.push({
        priority: 1,
        title: 'Improve cookie consent banner',
        description: 'Ensure banner includes both accept and reject options with clear labeling',
        implementation: 'Add missing buttons and ensure GDPR compliance',
        effort: 'moderate',
        impact: 'high',
      });
    }

    if (!analysis.hasRejectButton) {
      recommendations.push({
        priority: 2,
        title: 'Add reject button',
        description: 'Include a clear reject/decline option for users',
        implementation: 'Add reject button with equal prominence to accept button',
        effort: 'minimal',
        impact: 'high',
      });
    }

    return recommendations;
  }
}
```

## Step 4: Security Headers Check (Rule 10)

### 4.1 Create Security Headers Check
Create `backend/src/compliance/gdpr/checks/security-headers.ts`:

```typescript
import { GdprCheckResult, Evidence } from '../types';

export interface SecurityHeadersCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class SecurityHeadersCheck {
  /**
   * Check security headers for privacy by design
   * REAL ANALYSIS - checks actual HTTP headers
   */
  async performCheck(config: SecurityHeadersCheckConfig): Promise<GdprCheckResult> {
    const evidence: Evidence[] = [];
    let score = 0;
    const maxScore = 100;

    try {
      const response = await fetch(config.targetUrl, {
        method: 'HEAD',
        redirect: 'follow',
      });

      const headers = response.headers;

      // Check Content Security Policy
      const csp = headers.get('content-security-policy');
      if (csp) {
        score += 25;
        evidence.push({
          type: 'network',
          description: 'Content Security Policy header found',
          value: csp.substring(0, 100) + '...',
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'Content Security Policy header missing',
          value: 'Recommended for data protection',
        });
      }

      // Check X-Frame-Options
      const xFrame = headers.get('x-frame-options');
      if (xFrame) {
        score += 20;
        evidence.push({
          type: 'network',
          description: 'X-Frame-Options header found',
          value: xFrame,
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'X-Frame-Options header missing',
          value: 'Prevents clickjacking attacks',
        });
      }

      // Check X-Content-Type-Options
      const xContentType = headers.get('x-content-type-options');
      if (xContentType === 'nosniff') {
        score += 15;
        evidence.push({
          type: 'network',
          description: 'X-Content-Type-Options header properly set',
          value: xContentType,
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'X-Content-Type-Options header missing or incorrect',
          value: 'Should be set to "nosniff"',
        });
      }

      // Check Referrer Policy
      const referrerPolicy = headers.get('referrer-policy');
      if (referrerPolicy) {
        score += 20;
        evidence.push({
          type: 'network',
          description: 'Referrer-Policy header found',
          value: referrerPolicy,
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'Referrer-Policy header missing',
          value: 'Controls referrer information sharing',
        });
      }

      // Check Permissions Policy
      const permissionsPolicy = headers.get('permissions-policy');
      if (permissionsPolicy) {
        score += 20;
        evidence.push({
          type: 'network',
          description: 'Permissions-Policy header found',
          value: permissionsPolicy.substring(0, 100) + '...',
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'Permissions-Policy header missing',
          value: 'Controls browser feature access',
        });
      }

      const passed = score >= 60; // 60% threshold

      return {
        ruleId: 'GDPR-010',
        ruleName: 'Security Headers (Privacy by Design)',
        category: 'security',
        passed,
        score,
        weight: 5,
        severity: 'medium',
        evidence,
        recommendations: passed ? [] : [
          {
            priority: 1,
            title: 'Implement security headers',
            description: 'Add missing security headers for privacy by design',
            implementation: 'Configure web server with CSP, X-Frame-Options, and other security headers',
            effort: 'moderate',
            impact: 'medium',
          },
        ],
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-010',
        ruleName: 'Security Headers (Privacy by Design)',
        category: 'security',
        passed: false,
        score: 0,
        weight: 5,
        severity: 'medium',
        evidence: [{
          type: 'text',
          description: 'Security headers check failed',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Fix website accessibility for security analysis',
          description: 'Ensure website is accessible for security header analysis',
          implementation: 'Check website loading and network accessibility',
          effort: 'minimal',
          impact: 'medium',
        }],
        manualReviewRequired: false,
      };
    }
  }
}
```

## Step 5: Data Subject Rights Check (Rule 12)

### 5.1 Create Data Subject Rights Check
Create `backend/src/compliance/gdpr/checks/data-rights.ts`:

```typescript
import puppeteer, { Page } from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';

export interface DataRightsCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class DataRightsCheck {
  /**
   * Check for data subject rights information and links
   * REAL ANALYSIS - scans actual webpage content
   */
  async performCheck(config: DataRightsCheckConfig): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');

      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Search for data subject rights information
      const rightsAnalysis = await this.analyzeDataSubjectRights(page);

      // Score based on rights found
      const totalRights = 8; // GDPR defines 8 key rights
      const foundRights = rightsAnalysis.rightsFound.length;
      score = Math.round((foundRights / totalRights) * 100);

      // Add evidence for found rights
      for (const right of rightsAnalysis.rightsFound) {
        evidence.push({
          type: 'text',
          description: `Data subject right found: ${right.name}`,
          location: right.location,
          value: right.description,
        });
      }

      // Add evidence for missing rights
      for (const missingRight of rightsAnalysis.missingRights) {
        evidence.push({
          type: 'text',
          description: `Missing data subject right: ${missingRight}`,
          value: 'Required by GDPR',
        });
      }

      // Check for contact mechanisms
      const contactMechanisms = await this.findContactMechanisms(page);
      if (contactMechanisms.length > 0) {
        score += 20; // Bonus for contact mechanisms
        evidence.push({
          type: 'element',
          description: 'Contact mechanisms found for exercising rights',
          value: `${contactMechanisms.length} contact methods available`,
        });
      }

      const passed = score >= 70;

      return {
        ruleId: 'GDPR-012',
        ruleName: 'Data Subject Rights',
        category: 'data_rights',
        passed,
        score: Math.min(100, score),
        weight: 7,
        severity: 'high',
        evidence,
        recommendations: this.generateDataRightsRecommendations(rightsAnalysis, contactMechanisms),
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-012',
        ruleName: 'Data Subject Rights',
        category: 'data_rights',
        passed: false,
        score: 0,
        weight: 7,
        severity: 'high',
        evidence: [{
          type: 'text',
          description: 'Data rights check failed',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Add data subject rights information',
          description: 'Include comprehensive information about GDPR data subject rights',
          implementation: 'Add section explaining user rights and how to exercise them',
          effort: 'moderate',
          impact: 'high',
        }],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze data subject rights mentioned on the page
   */
  private async analyzeDataSubjectRights(page: Page): Promise<{
    rightsFound: Array<{ name: string; description: string; location: string }>;
    missingRights: string[];
  }> {
    return await page.evaluate(() => {
      const gdprRights = [
        { name: 'Right to Access', keywords: ['access', 'view', 'obtain', 'copy'] },
        { name: 'Right to Rectification', keywords: ['rectification', 'correct', 'update', 'amend'] },
        { name: 'Right to Erasure', keywords: ['erasure', 'delete', 'removal', 'right to be forgotten'] },
        { name: 'Right to Restrict Processing', keywords: ['restrict', 'limit', 'suspend'] },
        { name: 'Right to Data Portability', keywords: ['portability', 'transfer', 'export'] },
        { name: 'Right to Object', keywords: ['object', 'opt-out', 'refuse'] },
        { name: 'Right to Withdraw Consent', keywords: ['withdraw', 'revoke', 'consent'] },
        { name: 'Right not to be subject to Automated Decision-making', keywords: ['automated', 'profiling', 'algorithmic'] },
      ];

      const pageText = document.body.textContent?.toLowerCase() || '';
      const rightsFound: Array<{ name: string; description: string; location: string }> = [];
      const missingRights: string[] = [];

      for (const right of gdprRights) {
        const found = right.keywords.some(keyword => pageText.includes(keyword.toLowerCase()));

        if (found) {
          // Try to find the specific section
          const elements = document.querySelectorAll('*');
          let location = 'Page content';

          for (const element of elements) {
            const elementText = element.textContent?.toLowerCase() || '';
            if (right.keywords.some(keyword => elementText.includes(keyword.toLowerCase()))) {
              const parent = element.closest('section, div[class*="privacy"], div[class*="rights"]');
              if (parent) {
                location = parent.tagName.toLowerCase();
                break;
              }
            }
          }

          rightsFound.push({
            name: right.name,
            description: `Found references to ${right.keywords.join(', ')}`,
            location,
          });
        } else {
          missingRights.push(right.name);
        }
      }

      return { rightsFound, missingRights };
    });
  }

  /**
   * Find contact mechanisms for exercising rights
   */
  private async findContactMechanisms(page: Page): Promise<string[]> {
    return await page.evaluate(() => {
      const mechanisms: string[] = [];

      // Look for email addresses
      const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
      const pageText = document.body.textContent || '';
      const emails = pageText.match(emailPattern);

      if (emails && emails.length > 0) {
        mechanisms.push('Email contact');
      }

      // Look for contact forms
      const forms = document.querySelectorAll('form');
      for (const form of forms) {
        const formText = form.textContent?.toLowerCase() || '';
        if (formText.includes('contact') || formText.includes('privacy') || formText.includes('rights')) {
          mechanisms.push('Contact form');
          break;
        }
      }

      // Look for phone numbers
      const phonePattern = /(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g;
      const phones = pageText.match(phonePattern);

      if (phones && phones.length > 0) {
        mechanisms.push('Phone contact');
      }

      // Look for postal addresses
      const addressKeywords = ['address', 'street', 'avenue', 'road', 'suite', 'floor'];
      const hasAddress = addressKeywords.some(keyword =>
        pageText.toLowerCase().includes(keyword)
      );

      if (hasAddress) {
        mechanisms.push('Postal address');
      }

      return mechanisms;
    });
  }

  /**
   * Generate recommendations for data rights compliance
   */
  private generateDataRightsRecommendations(
    rightsAnalysis: any,
    contactMechanisms: string[]
  ): any[] {
    const recommendations: any[] = [];

    if (rightsAnalysis.missingRights.length > 0) {
      recommendations.push({
        priority: 1,
        title: 'Add missing data subject rights information',
        description: `Include information about: ${rightsAnalysis.missingRights.join(', ')}`,
        implementation: 'Add comprehensive section explaining all GDPR data subject rights',
        effort: 'moderate',
        impact: 'high',
      });
    }

    if (contactMechanisms.length === 0) {
      recommendations.push({
        priority: 2,
        title: 'Provide contact mechanisms',
        description: 'Add clear contact information for exercising data subject rights',
        implementation: 'Include email, contact form, or other means for users to contact you',
        effort: 'minimal',
        impact: 'high',
      });
    }

    return recommendations;
  }
}
```

## Next Steps
Continue with Part 04: Cookie Analysis and Tracking to implement cookie classification and tracker detection.

## Validation Checklist
- [ ] All checks perform real website analysis (no mock data)
- [ ] TypeScript compiles without errors (no `any` types)
- [ ] Evidence collection from actual website content
- [ ] Proper error handling with type guards
- [ ] Recommendations based on real findings
- [ ] Ready for cookie and tracker analysis implementation
