// GDPR Core Types - Strict TypeScript following .projectrules
// NO any[] types allowed - all arrays must specify element types

import { z } from 'zod';

// Core GDPR Rule Identifiers
export type GdprRuleId = 
  | 'GDPR-001' | 'GDPR-002' | 'GDPR-003' | 'GDPR-004' | 'GDPR-005'
  | 'GDPR-006' | 'GDPR-007' | 'GDPR-008' | 'GDPR-009' | 'GDPR-010'
  | 'GDPR-011' | 'GDPR-012' | 'GDPR-013' | 'GDPR-014' | 'GDPR-015'
  | 'GDPR-016' | 'GDPR-017' | 'GDPR-018' | 'GDPR-019' | 'GDPR-020'
  | 'GDPR-021';

export type GdprCategory = 
  | 'security' | 'privacy_policy' | 'consent' | 'cookies' 
  | 'data_rights' | 'data_protection' | 'organizational';

export type Severity = 'critical' | 'high' | 'medium' | 'low';
export type RiskLevel = 'critical' | 'high' | 'medium' | 'low';
export type ScanStatus = 'pending' | 'running' | 'completed' | 'failed';

// Zod Schemas for Validation
export const GdprScanRequestSchema = z.object({
  targetUrl: z.string().url('Valid URL is required'),
  scanOptions: z.object({
    enableCookieAnalysis: z.boolean().optional().default(true),
    enableTrackerDetection: z.boolean().optional().default(true),
    enableConsentTesting: z.boolean().optional().default(true),
    maxPages: z.number().int().min(1).max(50).optional().default(10),
    timeout: z.number().int().min(60000).max(3600000).optional().default(300000),
    userAgent: z.string().optional().default('GDPR-Scanner/1.0'),
  }).optional().default({}),
});

export type GdprScanRequest = z.infer<typeof GdprScanRequestSchema>;

// Core Interfaces
export interface GdprScanResult {
  scanId: string;
  targetUrl: string;
  timestamp: string;
  scanDuration: number;
  overallScore: number;
  riskLevel: RiskLevel;
  status: ScanStatus;
  summary: GdprScanSummary;
  checks: GdprCheckResult[];
  recommendations: GdprRecommendation[];
  metadata: GdprScanMetadata;
}

export interface GdprScanSummary {
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  manualReviewRequired: number;
  criticalFailures: number;
  categoryBreakdown: CategoryBreakdown[];
}

export interface CategoryBreakdown {
  category: GdprCategory;
  score: number;
  checksInCategory: number;
  passedInCategory: number;
}

export interface GdprCheckResult {
  ruleId: GdprRuleId;
  ruleName: string;
  category: GdprCategory;
  passed: boolean;
  score: number;
  weight: number;
  severity: Severity;
  evidence: Evidence[];
  recommendations: Recommendation[];
  manualReviewRequired: boolean;
}

export interface Evidence {
  type: 'text' | 'element' | 'network' | 'cookie';
  description: string;
  location?: string;
  value?: string;
}

export interface Recommendation {
  priority: number;
  title: string;
  description: string;
  implementation: string;
  effort: 'minimal' | 'moderate' | 'significant';
  impact: 'low' | 'medium' | 'high';
}

export interface GdprRecommendation {
  id: string;
  priority: number;
  title: string;
  description: string;
  category: GdprCategory;
  effort: 'minimal' | 'moderate' | 'significant';
  impact: 'low' | 'medium' | 'high';
  timeline: string;
  relatedRules: GdprRuleId[];
}

export interface GdprScanMetadata {
  version: string;
  processingTime: number;
  checksPerformed: number;
  analysisLevelsUsed: string[];
  errors: string[];
  warnings: string[];
  userAgent: string;
  scanOptions: GdprScanRequest['scanOptions'];
}

// Cookie-specific types
export interface Cookie {
  name: string;
  value: string;
  domain: string;
  path: string;
  secure: boolean;
  httpOnly: boolean;
  sameSite: 'Strict' | 'Lax' | 'None' | undefined;
  expires?: Date;
}

export interface CookieAnalysisResult {
  cookieId: string;
  category: 'essential' | 'analytics' | 'marketing' | 'functional';
  hasConsent: boolean;
  complianceIssues: string[];
  recommendations: string[];
}

// Consent-specific types
export interface ConsentBannerInfo {
  isPresent: boolean;
  bannerText: string;
  hasAcceptButton: boolean;
  hasRejectButton: boolean;
  hasGranularOptions: boolean;
  privacyPolicyLinked: boolean;
  complianceIssues: string[];
}

// Tracker-specific types
export interface Tracker {
  domain: string;
  name: string;
  category: TrackerCategory;
  purpose: string;
  dataCollected: string[];
  hasConsentMechanism: boolean;
  loadedBeforeConsent: boolean;
}

export type TrackerCategory = 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';

// Database entity types
export interface GdprScanEntity {
  id: string;
  user_id: string;
  target_url: string;
  scan_timestamp: Date;
  scan_duration: number;
  overall_score: number;
  risk_level: RiskLevel;
  total_checks: number;
  passed_checks: number;
  failed_checks: number;
  manual_review_required: number;
  scan_status: ScanStatus;
  error_message?: string;
  metadata: Record<string, unknown>;
  created_at: Date;
  updated_at: Date;
}

export interface GdprCheckResultEntity {
  id: string;
  scan_id: string;
  rule_id: GdprRuleId;
  rule_name: string;
  category: GdprCategory;
  passed: boolean;
  score: number;
  weight: number;
  severity: Severity;
  manual_review_required: boolean;
  evidence: Record<string, unknown>;
  recommendations: Record<string, unknown>;
  created_at: Date;
}
