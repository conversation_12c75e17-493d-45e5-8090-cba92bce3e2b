# GDPR Implementation Plan - Part 03B: Additional Individual Checks

## Overview
This document continues the individual GDPR compliance checks implementation. Each check performs real website analysis - NO MOCK DATA allowed.

## ⚠️ CRITICAL REQUIREMENTS
- **Real Website Analysis**: All checks must analyze actual websites using live HTTP requests
- **No Simulated Data**: Prohibited - use real content, cookies, and network traffic
- **TypeScript Strict Mode**: NO `any` types - follow .projectrules strictly
- **Comprehensive Evidence**: Collect real evidence from actual website scanning

## Step 6: Privacy Notice Content Analysis (Rule 3)

### 6.1 Create Privacy Content Check
Create `backend/src/compliance/gdpr/checks/privacy-content.ts`:

```typescript
import puppeteer, { Page } from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';

export interface PrivacyContentCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class PrivacyContentCheck {
  /**
   * Analyze privacy policy content for GDPR requirements
   * REAL ANALYSIS - analyzes actual privacy policy text
   */
  async performCheck(config: PrivacyContentCheckConfig): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Find privacy policy links
      const privacyLinks = await this.findPrivacyPolicyLinks(page);
      
      if (privacyLinks.length === 0) {
        evidence.push({
          type: 'text',
          description: 'No privacy policy found for content analysis',
          value: 'Cannot analyze privacy notice content',
        });
        
        return this.createFailedResult(evidence, 'No privacy policy found');
      }

      // Analyze the first privacy policy found
      const privacyUrl = privacyLinks[0].href;
      await page.goto(privacyUrl, { waitUntil: 'networkidle2', timeout: config.timeout });

      // Extract privacy policy text
      const privacyText = await page.evaluate(() => {
        return document.body.textContent || '';
      });

      // Analyze GDPR content requirements
      const contentAnalysis = this.analyzeGdprContent(privacyText);
      
      // Calculate score based on required elements found
      const totalRequirements = 10; // Key GDPR requirements
      const foundRequirements = contentAnalysis.foundElements.length;
      score = Math.round((foundRequirements / totalRequirements) * 100);

      // Add evidence for found elements
      for (const element of contentAnalysis.foundElements) {
        evidence.push({
          type: 'text',
          description: `GDPR requirement found: ${element.name}`,
          value: element.evidence,
          location: 'Privacy policy',
        });
      }

      // Add evidence for missing elements
      for (const missing of contentAnalysis.missingElements) {
        evidence.push({
          type: 'text',
          description: `Missing GDPR requirement: ${missing}`,
          value: 'Required by GDPR Article 13/14',
        });
      }

      const passed = score >= 70;

      return {
        ruleId: 'GDPR-003',
        ruleName: 'Privacy Notice Content',
        category: 'privacy_policy',
        passed,
        score,
        weight: 7,
        severity: 'high',
        evidence,
        recommendations: this.generateContentRecommendations(contentAnalysis),
        manualReviewRequired: true, // Legal adequacy requires manual review
      };

    } catch (error) {
      return this.createFailedResult([{
        type: 'text',
        description: 'Privacy content analysis failed',
        value: error instanceof Error ? error.message : 'Unknown error',
      }], 'Analysis failed');
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Find privacy policy links on the page
   */
  private async findPrivacyPolicyLinks(page: Page): Promise<Array<{
    text: string;
    href: string;
  }>> {
    return await page.evaluate(() => {
      const links: Array<{ text: string; href: string }> = [];
      const privacyPatterns = [
        /privacy\s*policy/i,
        /privacy\s*notice/i,
        /privacy\s*statement/i,
        /data\s*protection/i,
      ];

      const allLinks = document.querySelectorAll('a[href]');
      
      allLinks.forEach((link) => {
        const text = link.textContent?.trim() || '';
        const href = (link as HTMLAnchorElement).href;
        
        const matchesPattern = privacyPatterns.some(pattern => 
          pattern.test(text) || pattern.test(href)
        );
        
        if (matchesPattern) {
          links.push({ text, href });
        }
      });

      return links;
    });
  }

  /**
   * Analyze privacy policy content for GDPR requirements
   */
  private analyzeGdprContent(text: string): {
    foundElements: Array<{ name: string; evidence: string }>;
    missingElements: string[];
  } {
    const lowerText = text.toLowerCase();
    const foundElements: Array<{ name: string; evidence: string }> = [];
    const missingElements: string[] = [];

    const gdprRequirements = [
      {
        name: 'Controller Identity',
        patterns: ['controller', 'data controller', 'company name', 'organization'],
        required: true,
      },
      {
        name: 'Contact Information',
        patterns: ['contact', 'email', 'address', 'phone'],
        required: true,
      },
      {
        name: 'Processing Purposes',
        patterns: ['purpose', 'why we collect', 'use of data', 'processing'],
        required: true,
      },
      {
        name: 'Legal Basis',
        patterns: ['legal basis', 'lawful basis', 'consent', 'legitimate interest'],
        required: true,
      },
      {
        name: 'Data Categories',
        patterns: ['personal data', 'information we collect', 'data types'],
        required: true,
      },
      {
        name: 'Recipients',
        patterns: ['share', 'third party', 'recipients', 'disclosure'],
        required: true,
      },
      {
        name: 'Retention Periods',
        patterns: ['retention', 'how long', 'storage period', 'delete'],
        required: true,
      },
      {
        name: 'Data Subject Rights',
        patterns: ['your rights', 'data subject', 'access', 'rectification'],
        required: true,
      },
      {
        name: 'International Transfers',
        patterns: ['transfer', 'international', 'third country', 'outside'],
        required: false,
      },
      {
        name: 'DPO Contact',
        patterns: ['data protection officer', 'dpo', 'privacy officer'],
        required: false,
      },
    ];

    for (const requirement of gdprRequirements) {
      const found = requirement.patterns.some(pattern => lowerText.includes(pattern));
      
      if (found) {
        // Find evidence text
        const patternFound = requirement.patterns.find(pattern => lowerText.includes(pattern));
        const index = lowerText.indexOf(patternFound!);
        const evidence = text.substring(Math.max(0, index - 50), index + 100);
        
        foundElements.push({
          name: requirement.name,
          evidence: evidence.trim(),
        });
      } else if (requirement.required) {
        missingElements.push(requirement.name);
      }
    }

    return { foundElements, missingElements };
  }

  /**
   * Generate content-specific recommendations
   */
  private generateContentRecommendations(analysis: {
    missingElements: string[];
  }): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (analysis.missingElements.length > 0) {
      recommendations.push({
        priority: 1,
        title: 'Add missing GDPR content requirements',
        description: `Privacy policy missing: ${analysis.missingElements.join(', ')}`,
        implementation: 'Update privacy policy to include all required GDPR elements',
        effort: 'significant',
        impact: 'high',
      });
    }

    recommendations.push({
      priority: 2,
      title: 'Manual legal review required',
      description: 'Privacy policy content requires legal adequacy assessment',
      implementation: 'Have legal expert review privacy policy for GDPR compliance',
      effort: 'moderate',
      impact: 'high',
    });

    return recommendations;
  }

  /**
   * Create failed result
   */
  private createFailedResult(evidence: Evidence[], reason: string): GdprCheckResult {
    return {
      ruleId: 'GDPR-003',
      ruleName: 'Privacy Notice Content',
      category: 'privacy_policy',
      passed: false,
      score: 0,
      weight: 7,
      severity: 'high',
      evidence,
      recommendations: [{
        priority: 1,
        title: 'Create comprehensive privacy policy',
        description: `${reason} - implement GDPR-compliant privacy notice`,
        implementation: 'Create privacy policy with all required GDPR elements',
        effort: 'significant',
        impact: 'high',
      }],
      manualReviewRequired: true,
    };
  }
}
```

## Step 7: IP Anonymization Check (Rule 11)

### 7.1 Create IP Anonymization Check
Create `backend/src/compliance/gdpr/checks/ip-anonymization.ts`:

```typescript
import puppeteer, { Page } from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';

export interface IpAnonymizationCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class IpAnonymizationCheck {
  /**
   * Check for IP anonymization in analytics scripts
   * REAL ANALYSIS - analyzes actual JavaScript code
   */
  async performCheck(config: IpAnonymizationCheckConfig): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');
      
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Analyze JavaScript for analytics configurations
      const analyticsAnalysis = await this.analyzeAnalyticsScripts(page);
      
      // Check Google Analytics specifically
      const gaAnalysis = await this.checkGoogleAnalytics(page);
      
      // Calculate score based on findings
      let totalAnalytics = analyticsAnalysis.analyticsFound.length;
      let anonymizedAnalytics = 0;

      // Google Analytics check
      if (gaAnalysis.found) {
        totalAnalytics++;
        if (gaAnalysis.anonymized) {
          anonymizedAnalytics++;
          evidence.push({
            type: 'text',
            description: 'Google Analytics IP anonymization enabled',
            value: gaAnalysis.evidence,
          });
        } else {
          evidence.push({
            type: 'text',
            description: 'Google Analytics found without IP anonymization',
            value: 'anonymizeIp not detected',
          });
        }
      }

      // Other analytics platforms
      for (const analytics of analyticsAnalysis.analyticsFound) {
        if (analytics.hasAnonymization) {
          anonymizedAnalytics++;
          evidence.push({
            type: 'text',
            description: `${analytics.platform} has IP anonymization`,
            value: analytics.evidence,
          });
        } else {
          evidence.push({
            type: 'text',
            description: `${analytics.platform} lacks IP anonymization`,
            value: 'No anonymization detected',
          });
        }
      }

      // Calculate score
      if (totalAnalytics === 0) {
        score = 100; // No analytics = no IP collection issue
        evidence.push({
          type: 'text',
          description: 'No analytics platforms detected',
          value: 'No IP collection concerns',
        });
      } else {
        score = Math.round((anonymizedAnalytics / totalAnalytics) * 100);
      }

      const passed = score >= 80;

      return {
        ruleId: 'GDPR-011',
        ruleName: 'IP Address as Personal Data',
        category: 'data_protection',
        passed,
        score,
        weight: 5,
        severity: 'medium',
        evidence,
        recommendations: this.generateIpRecommendations(totalAnalytics, anonymizedAnalytics),
        manualReviewRequired: false,
      };

    } catch (error) {
      return {
        ruleId: 'GDPR-011',
        ruleName: 'IP Address as Personal Data',
        category: 'data_protection',
        passed: false,
        score: 0,
        weight: 5,
        severity: 'medium',
        evidence: [{
          type: 'text',
          description: 'IP anonymization check failed',
          value: error instanceof Error ? error.message : 'Unknown error',
        }],
        recommendations: [{
          priority: 1,
          title: 'Implement IP anonymization',
          description: 'Enable IP anonymization in all analytics platforms',
          implementation: 'Configure analytics tools to anonymize IP addresses',
          effort: 'minimal',
          impact: 'medium',
        }],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Check Google Analytics configuration
   */
  private async checkGoogleAnalytics(page: Page): Promise<{
    found: boolean;
    anonymized: boolean;
    evidence: string;
  }> {
    return await page.evaluate(() => {
      // Check for Google Analytics
      const scripts = Array.from(document.querySelectorAll('script'));
      let found = false;
      let anonymized = false;
      let evidence = '';

      // Check for gtag configuration
      for (const script of scripts) {
        const content = script.textContent || '';
        
        // Look for Google Analytics
        if (content.includes('gtag') || content.includes('ga(') || content.includes('googletagmanager')) {
          found = true;
          
          // Check for IP anonymization
          if (content.includes('anonymize_ip') || content.includes('anonymizeIp')) {
            anonymized = true;
            evidence = 'anonymize_ip configuration found';
          } else {
            evidence = 'Google Analytics found without anonymize_ip';
          }
          break;
        }
      }

      // Check for global gtag function
      if (!found && typeof (window as any).gtag === 'function') {
        found = true;
        evidence = 'gtag function detected';
      }

      // Check for ga function
      if (!found && typeof (window as any).ga === 'function') {
        found = true;
        evidence = 'ga function detected';
      }

      return { found, anonymized, evidence };
    });
  }

  /**
   * Analyze other analytics scripts
   */
  private async analyzeAnalyticsScripts(page: Page): Promise<{
    analyticsFound: Array<{
      platform: string;
      hasAnonymization: boolean;
      evidence: string;
    }>;
  }> {
    return await page.evaluate(() => {
      const analyticsFound: Array<{
        platform: string;
        hasAnonymization: boolean;
        evidence: string;
      }> = [];

      const scripts = Array.from(document.querySelectorAll('script'));
      
      const analyticsPlatforms = [
        {
          name: 'Adobe Analytics',
          patterns: ['adobe', 'omniture', 's_code'],
          anonymizationPatterns: ['visitorAPI', 'anonymize'],
        },
        {
          name: 'Hotjar',
          patterns: ['hotjar'],
          anonymizationPatterns: ['anonymizeIP', 'disable'],
        },
        {
          name: 'Mixpanel',
          patterns: ['mixpanel'],
          anonymizationPatterns: ['ip', 'anonymize'],
        },
      ];

      for (const script of scripts) {
        const content = script.textContent?.toLowerCase() || '';
        const src = script.src?.toLowerCase() || '';
        const fullContent = content + ' ' + src;

        for (const platform of analyticsPlatforms) {
          const platformFound = platform.patterns.some(pattern => 
            fullContent.includes(pattern)
          );

          if (platformFound) {
            const hasAnonymization = platform.anonymizationPatterns.some(pattern =>
              fullContent.includes(pattern)
            );

            analyticsFound.push({
              platform: platform.name,
              hasAnonymization,
              evidence: hasAnonymization 
                ? 'Anonymization configuration detected'
                : 'No anonymization detected',
            });
          }
        }
      }

      return { analyticsFound };
    });
  }

  /**
   * Generate IP anonymization recommendations
   */
  private generateIpRecommendations(total: number, anonymized: number): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (total > anonymized) {
      recommendations.push({
        priority: 1,
        title: 'Enable IP anonymization',
        description: `${total - anonymized} analytics platform(s) need IP anonymization`,
        implementation: 'Configure analytics tools to anonymize IP addresses before processing',
        effort: 'minimal',
        impact: 'medium',
      });
    }

    if (total > 0) {
      recommendations.push({
        priority: 2,
        title: 'Document IP processing',
        description: 'Update privacy policy to explain IP address processing and anonymization',
        implementation: 'Add section about IP address handling in privacy policy',
        effort: 'minimal',
        impact: 'low',
      });
    }

    return recommendations;
  }
}
```

## Next Steps
Continue with remaining GDPR checks in Part 03C for complete implementation.

## Validation Checklist
- [ ] Privacy content analysis performs real text analysis
- [ ] IP anonymization check analyzes actual JavaScript code
- [ ] Manual review flags set appropriately
- [ ] TypeScript strict compliance maintained
- [ ] Evidence collection from real website content
